{"name": "Valuit Tokenization", "version": "0.0.1", "main": "./build/config/server/index.js", "description": "", "scripts": {"copy-proto": "cp -r ./src/_grpc/proto build/_grpc/proto", "copy-ejs": "cp -r ./src/utils/emailTemplate build/utils/emailTemplate", "build": "npx tsc && npm run copy-ejs --skipLibCheck && npm run copy-proto --skipLibCheck", "start": "node ./build/config/server/index.js", "start:dev": "concurrently \"nodemon ./build/config/server/index.js\"", "dev": "npm run format && nodemon ./src/server.ts", "format": "npx prettier --write . './src/**/*.{ts,tsx,js,jsx,json}'", "sonar": "node sonar.js"}, "dependencies": {"@google-cloud/storage": "^7.12.1", "@grpc/grpc-js": "^1.11.1", "@grpc/proto-loader": "^0.7.13", "@sendgrid/mail": "^8.1.3", "@types/bcrypt": "^5.0.2", "@types/moment": "^2.13.0", "axios": "^1.7.3", "bcrypt": "^5.1.1", "big.js": "^6.2.2", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.5", "ejs": "^3.1.10", "eslint": "^9.4.0", "express": "^4.21.2", "fs": "^0.0.1-security", "geoip-lite": "^1.4.10", "helmet": "^7.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "libphonenumber-js": "^1.11.4", "lodash": "^4.17.21", "moment": "^2.30.1", "mongoose": "^8.5.0", "multer": "^1.4.5-lts.1", "otplib": "^12.0.1", "pino": "^9.1.0", "pino-pretty": "^11.1.0", "qrcode": "^1.5.3", "redis": "^4.6.14", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "swagger-ui-express": "^5.0.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@types/big.js": "^6.2.2", "@types/chai": "^4.3.16", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/debug": "^4.1.12", "@types/ejs": "^3.1.5", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.6", "@types/mocha": "^10.0.6", "@types/multer": "^1.4.11", "@types/node": "^22.4.2", "@types/qrcode": "^1.5.5", "@types/supertest": "^6.0.2", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "chai": "^5.1.1", "concurrently": "^8.2.2", "jsdoc": "^4.0.3", "mocha": "^10.4.0", "nodemon": "^3.1.4", "sonarqube-scanner": "^4.2.0", "supertest": "^7.0.0", "ts-node": "^10.9.2", "typescript": "^5.4.5"}}