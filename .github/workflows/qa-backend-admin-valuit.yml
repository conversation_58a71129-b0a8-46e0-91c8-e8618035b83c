name: Deploy on qa-backend-admin-valuit

on:
  push:
    branches:
      - qa
  pull_request:
    branches:
      - qa
env:
  PROJECT_ID: valuit-tokenhub
  GKE_ZONE: us-central1-a

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      # Authenticate with GCP using service account key
      - name: Authenticate with GCP
        uses: google-github-actions/auth@v2

        with:
          credentials_json: '${{ secrets.GOOGLE_CREDENTIALS }}'
          project_id: valuit-tokenhub

      - name: Get Runner IP
        id: runner_ip
        run: echo "RUNNER_IP=$(curl -s ifconfig.me)/32"

      - name: Add Runner IP to Firewall
        run: |
          IP=${{ env.RUNNER_IP }}
          gcloud compute firewall-rules update qa-valuit-ssh \
            --allow tcp:22 \
            --source-ranges="${IP}" \
            --project ${{ env.PROJECT_ID }}

      - name: Docker Build
        run: |
          docker build -t backend-admin-valuit .

      - name: Login to GCR
        run: |
          echo "Y" | gcloud auth configure-docker us-central1-docker.pkg.dev

      - name: Docker Tag
        run: |
          docker tag backend-admin-valuit:latest us-central1-docker.pkg.dev/valuit-tokenhub/qa-valuit/backend-admin-valuit:${{ github.run_id }}

      - name: Docker Push
        run: |
          docker push us-central1-docker.pkg.dev/valuit-tokenhub/qa-valuit/backend-admin-valuit:${{ github.run_id }}

      - name: SSH into GCP VM and run script
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_QA_HOST }}
          username: ${{ secrets.SSH_QA_USERNAME }}
          key: ${{ secrets.SSH_QA_PRIVATE_KEY }}
          port: ${{ secrets.SSH_QA_PORT }}
          script: |
            cd /home/<USER>/backend/backend-admin
            echo "Y" | gcloud auth configure-docker us-central1-docker.pkg.dev
            export BACKEND_ADMIN_VALUIT=us-central1-docker.pkg.dev/valuit-tokenhub/qa-valuit/backend-admin-valuit:${{ github.run_id }}
            docker-compose down
            docker system prune -af
            docker-compose up -d
