import { KafkaMessage, Consumer } from 'kafkajs';
import { kafkaHelperService } from '../helpers/kafka.helper';
import UserService from '../component/userAuthentications/service';
import logger from '../helpers/logging/logger.helper';
import OfferingService from '../component/offerings/service';
import emailHelper from '../helpers/email.helper';
import transferService from '../component/transferagent/service';
import { kafkaTypeEnum, KycStatus, soketTypeEnum, UserType } from '../utils/common.interface';
import { offeringSchema } from '../component/offerings/models/offerings.model';
import EventLogSchemaModel from '../component/transactions/model/EventLogSchema.model';
import { userSchema } from '../component/userAuthentications/user.model';
import TransferRequestService from '../component/transfer/service';
// import socketHelper from '../helpers/socket.helper';
import { OfferingComponent } from '../component';
import { socketHelper } from '../helpers/socket.helper';

const baseurl = process.env.BASEURL;

class KafkaService {
  private consumers: Consumer | null = null;

  constructor() {
    this.initialize();
  }

  private async initialize() {
    try {
      this.consumers = await this.setupConsumer('user-to-admin', this.handleUserMessage.bind(this));
    } catch (error) {
      logger.error('Error initializing Kafka consumer for admin:', error);
      throw error;
    }
  }

  private async setupConsumer(topic: string, messageHandler: (message: KafkaMessage, topic: string, partition: number, offset: string) => void): Promise<Consumer> {
    try {
      const consumer = await kafkaHelperService.createConsumer('admin-user-group', topic, messageHandler);
      this.consumers = consumer;
      return consumer;
    } catch (error) {
      logger.error(`Error setting up consumer for topic ${topic}:`, error);
      throw error;
    }
  }

  private async handleUserMessage(message: KafkaMessage, topic: string, partition: number, offset: any) {
    try {
      let response: any;
      const body = JSON.parse(message.value.toString());
      const value = typeof body.value === 'string' ? JSON.parse(body.value) : body.value;
      console.log(value?.type, 'body data------------->>>>>>>>>>>>>>>>', value);
      const filter = { _id: value?._id || value?.offeringId };
      if (value?._id) {
        delete value?._id;
      }
      switch (value?.type) {
        case 'fee':
          if (value?.event == kafkaTypeEnum.ADMINWALLETUPDATED) {
            response = await userSchema.findOneAndUpdate({ userType: UserType.Admin }, value, {
              new: false,
              runValidators: true,
            });
          } else {
            response = await offeringSchema.findOneAndUpdate(filter, value, {
              new: false,
              runValidators: true,
              upsert: true,
            });
            const detail = {
              offeringId: response._id,
              status: response?.offeringFeeStatus,
            };
            socketHelper.sendMessageToUserWithoutAuth('temp_id', soketTypeEnum.ADMINFEEUPDATED, detail);
          }
          break;
        case kafkaTypeEnum.OFFERING:
          if (value?.projectDetails && value?.projectDetails?.isPrivate) {
            const { data } = await OfferingService.fetchOfferingDetails(filter, [], [], true);
            const oldOfferingMembers = data?.projectDetails?.offeringMembers || [];
            const newOfferingMembers = value?.projectDetails?.offeringMembers || [];
            // Find the extra (newly added) emails
            const newEmails = newOfferingMembers.filter((email: string) => !oldOfferingMembers.includes(email));
            if (newEmails.length > 0) {
              // Only send emails if there are new users
              const emailString = oldOfferingMembers.length === newOfferingMembers.length && newEmails.length === 0 ? newOfferingMembers : newEmails.join(', ');

              const emailDetail = {
                offeringName: value?.projectDetails?.offeringName,
                tokenTicker: value?.projectDetails?.tokenTicker,
                assetName: value?.projectDetails?.assetName,
                icon: value?.overview?.icon,
                baseurl,
              };

              emailHelper.sendEmailTemplate(emailString, UserType.Investor, emailDetail);
            }
          }
          response = await OfferingService.updateOfferingDetails(value, filter);

          const data = {
            offeringId: filter,
            userId: value.userId,
            //  userId: "67ac6a805cd07ea1b3a41add",
            status: value.status,
            // status:"APPROVED"
          };
          const key = `offering:${data.offeringId._id}:status:${data.status}`;
          socketHelper.sendMessageToUserWithoutAuth('temp_id', 'statusUpdateOffering', data);
          break;
        case kafkaTypeEnum.REQOFFERING:
          response = await OfferingService.requestOffering(value, filter);
          break;
        case kafkaTypeEnum.REQWHITELIST:
          response = await OfferingService.createWhitelistDetails(value, filter);
          break;
        case kafkaTypeEnum.FORCETRANSFERRED:
          response = await TransferRequestService.updateForceTransferDetails(value, filter);
          break;
        case kafkaTypeEnum.ORDER:
          response = await OfferingService.updateorderDetails(value, filter);
          break;
        case kafkaTypeEnum.SAVEEVENT:
          const { transactionHash } = value;
          if (transactionHash) {
            await EventLogSchemaModel.findOneAndUpdate(
              { transactionHash }, // Search by uniqueId (salt)
              value, // Update the fields
              { new: true, upsert: true }, // `new: true` returns the updated document, `upsert: true` creates a new document if no match is found
            );
          }
          break;
        case kafkaTypeEnum.WHITELIST:
          response = await OfferingService.updateWhitelistDetails(value, filter);
          break;
        case kafkaTypeEnum.USER:
          if (value?.onchainID) {
            value.kycStatus = KycStatus.APPROVED;
          }
          const filters = filter?._id ? filter : { walletAddress: value?.onchainID };
          response = await UserService.updateUserDetails(value, filters);
          break;

        default:
          logger.warn(`Unrecognized or missing type`);
          return;
      }

      if (!response?.error && this?.consumers) {
        const nextOffset = (parseInt(offset) + 1).toString();
        await this.consumers.commitOffsets([{ topic, partition, offset: nextOffset }]);
        logger.info(`Committed offset ${offset} for partition ${partition} on topic ${topic}`);
      } else {
        logger.warn('Consumer is not initialized or operation failed.');
      }
    } catch (error) {
      logger.error('handleUserMessage:', error);
      throw error;
    }
  }

  public async sendMessageToUser(message: any) {
    try {
      await kafkaHelperService.sendMessage('admin-to-user', [{ value: JSON.stringify(message) }]);
    } catch (error) {
      logger.error('Error sending message from admin to user:', error);
      throw error;
    }
  }
  public async sendMessageToTransferagnet(message: any) {
    try {
      console.log('sendMessageToTransferAgent============>>>>>>>', message);
      await kafkaHelperService.sendMessage('admin-to-transferagent', [{ value: JSON.stringify(message) }]);
    } catch (error) {
      logger.error('Error sending message from admin-to-transferagent:', error);
      throw error;
    }
  }

  public async sendMessageToNotification(message: any) {
    try {
      await kafkaHelperService.sendMessage('notifications', [{ value: JSON.stringify(message) }]);
    } catch (error) {
      logger.error(error, 'Error sending message from user to admin:');
      throw error;
    }
  }
}

export default new KafkaService();
