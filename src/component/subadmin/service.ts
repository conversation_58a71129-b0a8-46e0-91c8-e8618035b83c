import { ISubadminService } from './interface';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { PromiseResolve, UserType, offeringStatusEnum } from '../../utils/common.interface';
import { FilterQuery } from 'mongoose';
import logger from '../../helpers/logging/logger.helper';
import * as bcrypt from 'bcrypt';
import { permission, permissionSchema, IUpdatepermission } from './permission.model';
import { IPagination } from '../../utils/common.interface';
import { Module } from './module.model';
import { IUpdateUserModel, userSchema } from '../userAuthentications/user.model';
import { IUserModel } from '../userAuthentications/user.model';
import CommonHelper from '../../helpers/common.helper';
class SubadminService implements ISubadminService {
  async fetchModuleList(projection: any[], pagination: IPagination): Promise<PromiseResolve> {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 }, search } = pagination;
      const skip = (page - 1) * limit;

      const modules = await Module.find();

      return {
        data: {
          user: modules,
          currentPage: page,
        },
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      throw new Error(`Error fetching users: ${error.message}`);
    }
  }

  async createsubadmin(data: any): Promise<PromiseResolve> {
    try {
      const { email, password } = data;
      const modulelist = await Module.find();
      const hashedPassword = await bcrypt.hash(password, 10);
      data.password = hashedPassword;
      data.isEmailVerify = true;
      data.isOtpActive = false;
      data.userType = UserType.Subadmin;
      const createuser = await userSchema.create(data);
      const userId = createuser?._id;
      data.userId = userId;
      const moduleIds = new Set(modulelist.map((module) => module._id.toString()));
      for (const permission of data.permission) {
        if (!moduleIds.has(permission.moduelsId)) {
          throw new Error(`Module ID ${permission.moduelsId} does not exist in modules list`);
        }
      }
      try {
        const query = await permissionSchema.findOneAndUpdate(
          { email },
          { ...data }, // Spread data object to update individual fields
          { new: true, runValidators: true, upsert: true },
          // Return updated document
        );

        if (query) {
          return {
            status: RESPONSES.SUCCESS,
            error: false,
            message: RES_MSG.USER.USER_UPDATION_SUCCESS,
            data: query,
          };
        }

        return {
          status: RESPONSES.BAD_REQUEST,
          error: true,
          message: RES_MSG.ERROR_MSG.USER_UPDATION_ERROR,
        };
      } catch (permissionError) {
        await userSchema.findByIdAndDelete(createuser._id);
        return {
          status: RESPONSES.INTERNAL_SERVER_ERROR,
          error: true,
          message: 'Failed to save permissions. User creation rolled back.',
        };
      }
    } catch (error) {
      logger.error(error, 'updateOfferingDetails error');
      return {
        status: error?.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async fetchSubaminlist(filters: IUpdateUserModel, projection: any[], pagination: IPagination): Promise<PromiseResolve> {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 }, search } = pagination;
      const skip = (page - 1) * limit;
      // const query: FilterQuery<any> = {};
      const query: FilterQuery<any> = {
        userType: 'subadmin', // Ensures only 'Subadmin' users are fetched
        email: { $ne: process.env.EMAIL }, // Excludes a specific email if needed
      };
      const addFilter = (key: keyof IUpdateUserModel, value: any) => {
        if (typeof value !== 'undefined') query[key] = value;
      };

      if (search) {
        // Escape special characters for safe regex use
        const safeSearch = CommonHelper.escapeRegExp(search);

        // Create a regex that matches the search term anywhere in the string (not just the end)
        const regexPattern = new RegExp(safeSearch, 'i'); // 'i' makes the regex case-insensitive

        // Update query with $or condition for matching any of the fields
        query.$or = [{ email: { $regex: regexPattern } }, { name: { $regex: regexPattern } }, { mobile: { $regex: regexPattern } }];
      }

      query.email = {
        $ne: process.env.EMAIL,
      };
      query.userType = {
        $eq: UserType.Subadmin,
      };

      addFilter('isActive', filters.isActive);

      const totalCount = await userSchema.countDocuments(query).exec();

      const users = await userSchema.find(query).select(projection).sort(sort).skip(skip).limit(limit).exec();

      const totalPages = Math.ceil(totalCount / limit);

      return {
        data: {
          user: users,
          currentPage: page,
          totalPages,
          totalCount,
          nextPage: page < totalPages ? page + 1 : null,
          previousPage: page > 1 ? page - 1 : null,
        },
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      throw new Error(`Error fetching users: ${error.message}`);
    }
  }

  async updateSubadmin(data: any): Promise<PromiseResolve> {
    try {
      const { userId } = data;
      if (data.walletAddress) {
        await userSchema.findOneAndUpdate({ _id: userId }, { walletAddress: data.walletAddress });
      }
      const query = await permissionSchema.findOneAndUpdate(
        { userId },
        { ...data }, // Spread data object to update individual fields
        { new: true },
        // Return updated document
      );
      if (query) {
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.USER.USER_UPDATION_SUCCESS,
          data: query,
        };
      }

      return {
        status: RESPONSES.BAD_REQUEST,
        error: true,
        message: RES_MSG.ERROR_MSG.USER_UPDATION_ERROR,
      };
    } catch (error) {
      logger.error(error, 'updateOfferingDetails error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async fetchUserDetails(searchDetails: FilterQuery<IUserModel>, fields?: string[], excludeFields?: string[]): Promise<PromiseResolve> {
    try {
      const userDetailsQuery = userSchema.findOne(searchDetails);
      if (fields && fields.length > 0) {
        const fieldsString = fields.join(' ');
        userDetailsQuery.select(fieldsString);
      }

      if (excludeFields && excludeFields.length > 0) {
        const excludeFieldsString = excludeFields.map((field) => `-${field}`).join(' ');
        userDetailsQuery.select(excludeFieldsString);
      }

      const userDetails: IUserModel = await userDetailsQuery.exec();

      if (userDetails && userDetails.email) {
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
          data: {
            ...userDetails.toObject(),
          },
        };
      }
      return {
        status: RESPONSES.NOTFOUND,
        error: true,
        message: RES_MSG.COMMON.NO_USER,
      };
    } catch (error) {
      logger.error(error, 'fetchUserDetails error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async fetchSubAdminDetails(searchDetails: FilterQuery<IUserModel>, fields?: string[], excludeFields?: string[]): Promise<PromiseResolve> {
    try {
      const userDetailsQuery = permissionSchema.findOne(searchDetails);
      if (fields && fields.length > 0) {
        const fieldsString = fields.join(' ');
        userDetailsQuery.select(fieldsString);
      }

      if (excludeFields && excludeFields.length > 0) {
        const excludeFieldsString = excludeFields.map((field) => `-${field}`).join(' ');
        userDetailsQuery.select(excludeFieldsString);
      }

      const userDetails: permission = await userDetailsQuery.exec();

      if (userDetails && userDetails.email) {
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
          data: {
            ...userDetails.toObject(),
          },
        };
      }
      return {
        status: RESPONSES.NOTFOUND,
        error: true,
        message: RES_MSG.COMMON.NO_USER,
      };
    } catch (error) {
      logger.error(error, 'fetchUserDetails error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async fetchUserListcsv(filters: IUpdateUserModel, projection: any[]): Promise<PromiseResolve> {
    try {
      const query: FilterQuery<any> = {};
      const addFilter = (key: keyof IUpdateUserModel, value: any) => {
        if (typeof value !== 'undefined') query[key] = value;
      };

      query.email = {
        $ne: process.env.EMAIL,
      };
      query.userType = {
        $eq: UserType.Subadmin,
      };

      addFilter('createdAt', filters.createdAt);

      const totalCount = await userSchema.countDocuments(query).exec();

      const users = await userSchema.find(query).select(projection).sort({ createdAt: -1 });

      return {
        data: {
          user: users,
          totalCount,
        },

        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      throw new Error(`Error fetching users: ${error.message}`);
    }
  }
}
export default new SubadminService();
