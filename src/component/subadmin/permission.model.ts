import mongoose, { Schema, Document, Types } from 'mongoose';
import { OperationType, transferAgentStatus } from '../../utils/common.interface';

export interface permission extends Document {
  _id: string;
  email: string;
  userId?: Types.ObjectId | string;
  permission: Array<{
    read: Boolean;
    write: Boolean;
    // view: Boolean;
    moduelsId?: Types.ObjectId | string; // Array of ObjectIds to store offering IDs
  }>;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IUpdatepermission {
  _id: string;
  email: string;
  userId?: Types.ObjectId | string;
  permission: Array<{
    read: Boolean;
    write: Boolean;
    // view: Boolean;
    moduelsId?: Types.ObjectId | string; // Array of ObjectIds to store offering IDs
  }>;
  createdAt?: Date;
  updatedAt?: Date;
}

const permission: Schema<permission> = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: 'users', required: true },
    email: { type: String, required: true, unique: true },
    permission: {
      type: [
        {
          read: { type: Boolean, default: false },
          write: { type: Boolean, default: false },
          //   view: { type: Bo<PERSON>an, default: false },
          moduelsId: {
            type: Schema.Types.ObjectId,
            ref: 'Module',
            required: false,
          },
        },
      ],
      _id: false,
    },
  },
  { versionKey: false, timestamps: true },
);
permission.path('permission').validate(function (value: any[]) {
  const modulesIds = value.map((perm) => perm.moduelsId?.toString());
  return new Set(modulesIds).size === modulesIds.length;
}, 'Modules ID must be unique within the permission array for each email.');
permission.path('permission').validate(function (value: any[]) {
  return value.every((perm) => {
    if (perm.moduelsId?.toString() === '672c5c4a05e65803246ffc33') {
      return perm.read === false && perm.write === false;
    }
    return true;
  });
}, 'SubAdmin cannot have the permission of Manage SubAdmin module.');

const permissionSchema = mongoose.model<permission>('permission', permission);

export { permissionSchema };
