import { Request, Response } from 'express';
import { PromiseResolve, UserType } from '../../utils/common.interface';
// import OfferingService from "./service";
import transferService from './service';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import CustomError from '../../helpers/customError.helper';
import { ResponseHandler } from '../../helpers/response.helper';
import logger from '../../helpers/logging/logger.helper';
import mongoose from 'mongoose';
import UserService from '../userAuthentications/service';
import emailHelper from '../../helpers/email.helper';
import { permissionSchema } from './permission.model';
import OfferingService from '../offerings/service';
import { Module } from './module.model';
import subadmin from './service';
import * as bcrypt from 'bcrypt';
import { userSchema } from '../userAuthentications/user.model';
import RedisHelper from '../../helpers/redis.helper';
const baseurl = process.env.BASEURL;
const adminurl = process.env.ADMINURL;

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise < PromiseResolve >}
 */

export async function createSubadmin(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    let { name, email, password, confirmPassword, permission, walletAddress } = req.body;
    walletAddress = walletAddress?.toLowerCase();
    email = email?.toLowerCase();

    // const searchQuery = { email: email };
    const emailExists: PromiseResolve = await UserService.fetchUserDetails({ email: email }, [], [], true);
    const walletAddressExists: PromiseResolve = await UserService.fetchUserDetails({ walletAddress: walletAddress }, [], [], true);
    if (!emailExists.error) {
      throw new CustomError(RES_MSG.USER.USER_EXIST, RESPONSES.CONFLICT);
    }
    if (!walletAddressExists.error) {
      throw new CustomError(RES_MSG.USER.WALLET_EXIST, RESPONSES.CONFLICT);
    }
    const isTaWalletAddressExists = await UserService.searchUsersAndAgents(walletAddress);
    if (isTaWalletAddressExists.error) {
      throw new CustomError(RES_MSG.USER.WALLET_EXIST, RESPONSES.CONFLICT);
    }
    if (password !== confirmPassword) {
      throw new CustomError(RES_MSG.USER.PASSWORD, RESPONSES.BAD_REQUEST);
    }
    // const hashedPassword = await bcrypt.hash(password, 10);
    // const data = {
    //   name,
    //   email,
    //   password: hashedPassword,
    //   UserType: 'Subadmin',
    //   walletAddress: walletAddress,
    // };
    const createUserResp: PromiseResolve = await subadmin.createsubadmin({ ...req.body, name, email, password, confirmPassword, permission, walletAddress });
    if (createUserResp.error) {
      throw new CustomError(createUserResp?.message || RES_MSG.COMMON.SOMETHING_WRONG, createUserResp?.status || RESPONSES.CONFLICT);
    }
    const capitalizeFirstLetter = (str: string): string => {
      return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
    };

    const emailDetail = {
      name: capitalizeFirstLetter(name),
      email,
      adminurl,
      password,
    };
    emailHelper.sendEmailTemplate(email, 'SubAdminInvite', emailDetail);
    return ResponseHandler.success(res, {
      status: createUserResp.status || RESPONSES.SUCCESS,
      error: false,
      message: createUserResp.message || RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
      data: createUserResp.data,
    });
  } catch (error: any) {
    logger.error(error, 'getOffering Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

// export async function moduleList(
//   req: Request,
//   res: Response
// ): Promise<PromiseResolve> {
//   try {
//     const modulelist = await Module.find();
//     const email = req?.userInfo.email;
//     const role = req?.userInfo.userType;
//      const searchDetails= {email:email}
//      const permission =  await permissionSchema.findOne(searchDetails);
//     const pr=permission?.permission
//     const updatedModules = modulelist.map((module) => {
//       const permission = pr.find((perm:any) => perm.moduelsId.equals(module._id));
//       return {
//         id: module.id,
//         title: module.tittle,
//         read: role === 'admin'
//           ? true
//           : (module.tittle === 'Manage SubAdmin' ? false : (module.tittle === 'Dashboard' ? true : (permission ? permission.read : false))),
//         write: role === 'admin'
//           ? true
//           : (module.tittle === 'Manage SubAdmin' ? false : (module.tittle === 'Dashboard' ? true : (permission ? permission.write : false))),
//       };
//     });
//     return ResponseHandler.success(res, {
//       status: 200,
//       error: false,
//       message: RES_MSG.USER.USERS_FETCH,
//       data: updatedModules,
//     });
//   } catch (error) {
//     logger.error(error, "getUserslist Error");
//     return ResponseHandler.error(res, {
//       message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
//       status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
//       error: true,
//     });
//   }
// }

export async function moduleList(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const modulelist = await Module.find();
    const email = req?.userInfo.email;
    const role = req?.userInfo.userType;
    const searchDetails = { email: email };
    const permission = await permissionSchema.findOne(searchDetails);
    const pr = permission?.permission;

    // If role is admin, set read and write to true for all modules
    const updatedModules = modulelist.map((module) => {
      if (role === 'admin') {
        return {
          id: module.id,
          title: module.title,
          read: true,
          write: true,
        };
      }

      // For non-admin users, check permissions
      const modulePermission = pr.find((perm: any) => perm.moduelsId.equals(module._id));
      return {
        id: module.id,
        title: module.title,
        read: module.title === 'Manage SubAdmin' ? false : module.title === 'Dashboard' ? true : modulePermission ? modulePermission.read : false,
        write: module.title === 'Manage SubAdmin' ? false : module.title === 'Dashboard' ? true : modulePermission ? modulePermission.write : false,
      };
    });

    return ResponseHandler.success(res, {
      status: 200,
      error: false,
      message: RES_MSG.USER.USERS_FETCH,
      data: updatedModules,
    });
  } catch (error) {
    logger.error(error, 'getUserslist Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function getModuleListById(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const modulelist = await Module.find();

    const role = req?.userInfo.userType;
    if (role != UserType.Admin) {
      throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.CONFLICT);
    }

    return ResponseHandler.success(res, {
      status: 200,
      error: false,
      message: RES_MSG.USER.USERS_FETCH,
      data: modulelist,
    });
  } catch (error) {
    logger.error(error, 'getUserslist Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function getTransferAgentController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { userId } = req.params;
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return ResponseHandler.error(res, {
        message: 'invalid id',
        status: RESPONSES.BAD_REQUEST,
        error: true,
      });
    }

    const searchquery = { 'projectDetails.taId': userId };
    const user = await OfferingService.fetchOfferingDetails(searchquery);
    if (!user) {
      return ResponseHandler.error(res, {
        message: RES_MSG.ERROR_MSG.USER_NOT_FOUND,
        status: 400,
        error: true,
      });
    }

    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
      data: user,
    });
  } catch (error) {
    logger.error(error, 'getUserslist Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function transferAgentListCsv(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { sort = JSON.stringify({ createdAt: -1 }), startDate = '', endDate = '' } = req.query;

    // Initialize filters object
    const filters: any = {};

    // Add date filtering based on startDate and endDate
    if (startDate && endDate) {
      filters.createdAt = {
        $gte: new Date(startDate as string),
        $lte: new Date(endDate as string),
      };
    } else if (startDate) {
      filters.createdAt = { $gte: new Date(startDate as string) };
    } else if (endDate) {
      filters.createdAt = { $lte: new Date(endDate as string) };
    }

    // Parse sort criteria
    // let sortCriteria = JSON.parse(sort as string);

    // Define the projection fields
    const projection = ['name', 'email', 'isActive', 'noOfOfferings', 'offeringId', 'status', 'walletAddress', 'createdAt'];

    // Fetch user list with the applied filters and sorting
    const userDetails = await transferService.fetchUserListcsv(filters, projection);

    return ResponseHandler.success(res, {
      status: 200,
      error: false,
      message: RES_MSG.USER.USERS_FETCH,
      data: userDetails.data,
    });
  } catch (error) {
    logger.error(error, 'getUserslist Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise <PromiseResolve>}
 */
export async function unblock(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { email } = req.body;
    // Fetch user details and return specific fields
    const userDetails = await UserService.fetchUserDetails({ email }, ['name', 'email', 'isActive']);
    if (userDetails.error) {
      throw new CustomError(userDetails.message || RES_MSG.COMMON.SOMETHING_WRONG, userDetails.status || RESPONSES.BAD_REQUEST);
    }

    const currentIsActive = userDetails.data.isActive;

    // gRPC call to unblock the user
    // Update user's active status and reset kycCount if activated
    const updatedUser = await userSchema.findOneAndUpdate(
      { email },
      {
        isActive: currentIsActive ? false : true,
      },
      { new: true }, // Return the updated document
    );
    const emailDetails = { name: userDetails.data.name };
    let message;
    if (updatedUser.isActive) {
      emailHelper.sendEmailTemplate(email, 'subadminAccountUnBlock', emailDetails);
      message = RES_MSG.COMMON.ADMIN_UNBLOCK_SUBADMIN;
    } else {
      emailHelper.sendEmailTemplate(email, 'SubadminAccountBlock', emailDetails);
      message = RES_MSG.COMMON.ADMIN_BLOCK_SUBADMIN;
      await RedisHelper.deleteKey(`accessToken:${email}`);
    }

    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message,
      data: updatedUser.isActive,
    });
  } catch (error: any) {
    logger.error(error, 'unblock Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

export async function subAdminList(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const {
      page = 1,
      limit = 10,
      sort = JSON.stringify({ createdAt: -1 }),
      search = '',

      isActive,
    } = req.query;

    const filters = {
      ...(isActive === 'true' || isActive === 'false' ? { isActive: isActive === 'true' } : {}),
    };
    let sortCriteria = JSON.parse(sort as string);
    const projection = ['name', 'email', 'isActive', 'createdAt', 'lastLogin', 'walletAddress', 'onchainID'];

    const userDetails = await subadmin.fetchSubaminlist(filters, projection, {
      page: Number(page),
      limit: Number(limit),
      sort: sortCriteria,
      search,
    });

    return ResponseHandler.success(res, {
      status: 200,
      error: false,
      message: RES_MSG.USER.USERS_FETCH,
      data: userDetails.data,
    });
  } catch (error) {
    logger.error(error, 'getUserslist Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function getSubadminListControllerCsv(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { sort = JSON.stringify({ createdAt: -1 }), startDate = '', endDate = '' } = req.query;

    // Initialize filters object
    const filters: any = {
      ...(startDate && endDate
        ? { createdAt: { $gte: new Date(startDate as string), $lte: new Date(endDate as string) } }
        : startDate
        ? { createdAt: { $gte: new Date(startDate as string) } }
        : endDate
        ? { createdAt: { $lte: new Date(endDate as string) } }
        : {}),
    };

    // Parse sort criteria
    // let sortCriteria = JSON.parse(sort as string);

    // Define the projection fields
    const projection = ['name', 'email', 'isActive', 'createdAt', 'lastLogin', 'walletAddress', 'onchainID', 'status'];

    // Fetch user list with the applied filters and sorting
    const userDetails = await subadmin.fetchUserListcsv(filters, projection);

    return ResponseHandler.success(res, {
      status: 200,
      error: false,
      message: RES_MSG.USER.USERS_FETCH,
      data: userDetails.data,
    });
  } catch (error) {
    logger.error(error, 'getUserslist Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function getPermissionListById(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const modulelist = await Module.find();
    const id = req.query.userId;
    const searchDetails = { userId: id };
    let role: string;
    const permission = await permissionSchema.findOne(searchDetails);
    const details = { _id: id };
    const user = await userSchema.findOne(details);
    const pr = permission?.permission;

    // If role is admin, set read and write to true for all modules
    const updatedModules = modulelist.map((module) => {
      if (role === UserType.Admin) {
        return {
          id: module.id,
          title: module.title,
          read: true,
          write: true,
        };
      }
      // For non-admin users, check permissions
      const modulePermission = pr.find((perm: any) => perm.moduelsId.equals(module._id));
      return {
        id: module?.id,
        title: module?.title,
        read: module.title === 'Manage SubAdmin' ? false : module.title === 'Dashboard' ? true : module.title === 'Settings' ? true : modulePermission ? modulePermission.read : false,

        write: module.title === 'Manage SubAdmin' ? false : module.title === 'Dashboard' ? true : module.title === 'Settings' ? true : modulePermission ? modulePermission.write : false,
      };
    });
    const list = {
      name: user?.name,
      email: user?.email,
      walletAddress: user?.walletAddress,
      onchainID: user?.onchainID,
      updatedModules,
    };

    return ResponseHandler.success(res, {
      status: 200,
      error: false,
      message: RES_MSG.USER.USERS_FETCH,
      data: list,
    });
  } catch (error) {
    logger.error(error, 'getUserslist Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function updateStatus(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { userId, permission, walletAddress } = req.body;
    // const searchQuery = { email: email };
    const emailExists: PromiseResolve = await subadmin.fetchSubAdminDetails({ userId: userId }, [], [], true);
    if (emailExists.error) {
      throw new CustomError(RES_MSG.USER.USER_ALREADY_EXIST, RESPONSES.CONFLICT);
    }
    const permissionList = JSON.parse(JSON.stringify(permission));

    const createUserResp: PromiseResolve = await subadmin.updateSubadmin(req?.body);
    if (!createUserResp) {
    }
    const { email } = await userSchema.findOne({ _id: userId });
    await RedisHelper.deleteKey(`accessToken:${email}`);
    return ResponseHandler.success(res, {
      status: createUserResp.status || RESPONSES.SUCCESS,
      error: false,
      message: createUserResp.message || RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
      data: createUserResp.data,
    });
  } catch (error: any) {
    logger.error(error, 'getOffering Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

// Class-based wrapper - same functions, just wrapped in class object
class SubadminController {
  public createSubadmin = createSubadmin;
  public moduleList = moduleList;
  public getModuleListById = getModuleListById;
  public getTransferAgentController = getTransferAgentController;
  public transferAgentListCsv = transferAgentListCsv;
  public unblock = unblock;
  public subAdminList = subAdminList;
  public getSubadminListControllerCsv = getSubadminListControllerCsv;
  public getPermissionListById = getPermissionListById;
  public updateStatus = updateStatus;
}

export default new SubadminController();
