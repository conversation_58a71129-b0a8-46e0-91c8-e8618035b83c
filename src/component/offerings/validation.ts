import { JoiValidationResult } from '../../utils/common.interface';
import * as Joi from 'joi';
import * as joiOptions from '../../helpers/joiError.filter.helper';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import logger from '../../helpers/logging/logger.helper';
import { IOffering, IUpdateOffering } from './models/offerings.model';

class OfferingValidation {
  /**
   * Validate the offering details based on the current step
   * @returns {Promise<JoiValidationResult>}
   */

  /**
   * Validate the subscribe details .
   * @returns {Promise<JoiValidationResult>}
   */
  async offeringReportValidation(params: IOffering): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        offeringId: Joi.array().items(Joi.string().length(24).hex().required()).min(1).max(5).required().messages({
          'array.base': 'offeringId must be an array',
          'array.includes': 'offeringId must contain valid MongoDB ObjectIds',
          'string.length': 'Invalid Offering Id',
          'string.hex': 'offeringId must be a valid MongoDB ObjectId',
          'array.min': 'offeringId must contain at least 1 item',
          'array.max': 'offeringId must contain at most 5 items',
        }),
        period: Joi.string().valid('1Y', '2Y', '3Y', '1M', '15D', '7D', '1D').optional().default('1year').messages({
          'string.base': 'period must be a string',
          'string.empty': 'period cannot be empty',
          'any.only': 'period must be one of [1Y, 2Y, 3Y, 1M, 15D, 7D, 1D]',
        }),
      });
      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }
      return {
        error: false,
        value: value,
      };
    } catch (error) {
      logger.error(error, 'offeringReportValidation Error');
      return {
        error: true,
        value: '',
        message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: RESPONSES.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async updateWhitelistedOffering(query: { whitelistId: string; status?: string }): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        whitelistId: Joi.string().required(), // Validate offeringId as a required string
        status: Joi.string().required().default('REJECTED'),
      });

      const { error, value } = schema.validate(query, joiOptions.options);
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }

      return {
        error: false,
        value,
      };
    } catch (error) {
      logger.error(error, 'getOrdersfromOfferingIdValidation Error');
      return {
        error: true,
        value: '',
        message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: RESPONSES.INTERNAL_SERVER_ERROR,
      };
    }
  }
  async getOrdersfromOfferingIdValidation(query: { page?: number; limit?: number; offeringId: string; status?: string; searchQuery?: string }): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        offeringId: Joi.string().required(), // Validate offeringId as a required string
        page: Joi.number().integer().min(1).default(1).optional(),
        limit: Joi.number().integer().min(1).default(10).optional(),
        status: Joi.string().default('').optional(),
        searchQuery: Joi.string().default('').optional(),
      });

      const { error, value } = schema.validate(query, joiOptions.options);
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }

      return {
        error: false,
        value,
      };
    } catch (error) {
      logger.error(error, 'getOrdersfromOfferingIdValidation Error');
      return {
        error: true,
        value: '',
        message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: RESPONSES.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * Validate the offering details based on the current step
   * @returns {Promise<JoiValidationResult>}
   */
  async createOfferingsValidation(params: IOffering): Promise<JoiValidationResult> {
    try {
      let schemaToValidate;
      switch (params.currentStep) {
        case 1:
          schemaToValidate = joiOptions.currentStepSchema.append({
            overview: joiOptions.overviewSchema.required(),
            offeringId: Joi.string()
              .regex(/^[0-9a-fA-F]{24}$/)
              .required(),
            userId: Joi.string()
              .regex(/^[0-9a-fA-F]{24}$/)
              .required(),
          });
          break;
        case 2:
          schemaToValidate = joiOptions.currentStepSchema.append({
            projectDetails: joiOptions.projectDetailsSchema.required(),
            offeringId: Joi.string()
              .regex(/^[0-9a-fA-F]{24}$/)
              .required(),
            userId: Joi.string()
              .regex(/^[0-9a-fA-F]{24}$/)
              .required(),
          });
          break;
        case 3:
          if (!params.documents?.assetType) {
            // If `assetType` does not exist, skip validation and return the raw input
            return {
              error: false,
              value: params,
            };
          }
          schemaToValidate = joiOptions.currentStepSchema.append({
            documents: joiOptions.offeringDocumentsSchema.required(),
            offeringId: Joi.string()
              .regex(/^[0-9a-fA-F]{24}$/)
              .required(),
            userId: Joi.string()
              .regex(/^[0-9a-fA-F]{24}$/)
              .required(),
          });
          break;
        case 4:
          schemaToValidate = joiOptions.currentStepSchema.append({
            team: joiOptions.teamSchema.required(),
            offeringId: Joi.string()
              .regex(/^[0-9a-fA-F]{24}$/)
              .required(),
            userId: Joi.string()
              .regex(/^[0-9a-fA-F]{24}$/)
              .required(),
          });

          break;
        case 5:
          // Step 5: Final submission (Validate all fields)
          schemaToValidate = joiOptions.currentStepSchema.append({
            // overview: joiOptions.overviewSchema.required(),
            // projectDetails: joiOptions.projectDetailsSchema.required(),
            // documents: joiOptions.offeringDocumentsSchema.required(),
            // team: joiOptions.teamSchema.required(),
            isFinalSubmission: Joi.boolean().required().default(true),
            offeringId: Joi.string()
              .regex(/^[0-9a-fA-F]{24}$/)
              .required(),
            userId: Joi.string()
              .regex(/^[0-9a-fA-F]{24}$/)
              .required(),
          });
          break;
        default:
          return {
            error: false,
            value: {
              currentStep: 0,
              ...params,
            },
          };
      }

      const { error, value } = schemaToValidate.validate(params, joiOptions.options);
      if (error) {
        if (JSON.stringify(error)?.includes('Start Date must be greater than or equal to')) {
          return {
            error: true,
            value: '',
            message: RES_MSG.ERROR_MSG.START_DATE,
            status: RESPONSES.BAD_REQUEST,
          };
        }
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }

      return {
        error: false,
        value,
      };
    } catch (error) {
      logger.error(error, 'createOfferingsValidation Error');
      return {
        error: true,
        value: '',
        message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: RESPONSES.INTERNAL_SERVER_ERROR,
      };
    }
  }
}

export default new OfferingValidation();
