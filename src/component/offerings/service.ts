import { IOfferingsService } from './interface';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { PromiseResolve } from '../../utils/common.interface';
import { FilterQuery, Types } from 'mongoose';
import logger from '../../helpers/logging/logger.helper';
import { IOffering, offeringSchema, IUpdateOffering } from './models/offerings.model';
import { IPagination } from '../../utils/common.interface';
import { IUpdateWhitelist, whitelistSchema } from './models/whitelist.model';
import { IUpdateOrder, OrderSchema } from './models/order.model';
import CommonHelper from '../../helpers/common.helper';
import * as moment from 'moment';
import CustomError from '../../helpers/customError.helper';
import { IRequestedOffering, requestedOfferingSchema } from './models/requestedOfferings.model';
import { calculate } from '../../helpers/bigMath';
import { socketHelper } from '../../helpers/socket.helper';
import { userSchema } from '../userAuthentications/user.model';
import { transferAgentSchema } from '../transferagent/transferagent.model';
import emailHelper from '../../helpers/email.helper';

class OfferingService implements IOfferingsService {
  /**
   * @param {IOffering} searchDetails
   * @param {IOffering} fields
   * @param {IOffering} excludeFields
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  async fetchOfferingDetails(searchDetails: FilterQuery<IOffering>, fields?: string[], excludeFields?: string[]): Promise<PromiseResolve> {
    try {
      const query = offeringSchema.findOne(searchDetails);
      if (fields && fields.length > 0) {
        const fieldsString = fields.join(' ');
        query.select(fieldsString);
      }

      if (excludeFields && excludeFields.length > 0) {
        const excludeFieldsString = excludeFields.map((field) => `-${field}`).join(' ');
        query.select(excludeFieldsString);
      }
      const offeringDetails: IOffering | null = await query.exec();

      if (offeringDetails) {
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
          data: offeringDetails.toObject(),
        };
      } else {
        return {
          status: RESPONSES.NOTFOUND,
          error: true,
          message: RES_MSG.COMMON.NO_OFFERING,
        };
      }
    } catch (error) {
      logger.error(error, 'fetchOfferingDetails error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * @param {IRequestedOffering} searchDetails
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  async fetchAssignedOfferingDetails(searchDetails: FilterQuery<IRequestedOffering>): Promise<PromiseResolve> {
    try {
      const offeringDetails = await requestedOfferingSchema.findOne(searchDetails);

      if (offeringDetails) {
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
          data: offeringDetails.toObject(),
        };
      } else {
        return {
          status: RESPONSES.NOTFOUND,
          error: true,
          message: RES_MSG.COMMON.NO_OFFERING,
        };
      }
    } catch (error) {
      logger.error(error, 'fetchAssignedOfferingDetails error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * @param {I} data
   * @param {IOffering} filter
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  async updateOfferingDetails(data: IUpdateOffering, filter: FilterQuery<IOffering>): Promise<PromiseResolve> {
    try {
      if (data?.tokenAddress) {
        data.isTokenDeploy = true;
      }
      if (data?.fundAddress) {
        data.isFundDeploy = true;
        // data.status = offeringStatusEnum.APPROVED;
      }

      const updateUserResp = await offeringSchema.findOneAndUpdate(filter, data, {
        new: true,
        runValidators: true,
        upsert: true,
      });
      socketHelper.sendMessageToUserWithoutAuth('temp_id', 'activeOfferingUpdate', filter);

      const offeringId: any = updateUserResp?._id;
      const searchQuery = { _id: offeringId };

      const offeringDetails: PromiseResolve = await OfferingService.fetchOfferingDetails(searchQuery, [], [], true);
      if (offeringDetails.error) {
        throw new CustomError(offeringDetails.message, offeringDetails.status);
      }
      if (updateUserResp?.status === 'APPROVED' && updateUserResp?.createdBy.toString() !== updateUserResp?.userId.toString()) {
        await this.requestOffering({ status: data?.status }, filter);
      }

      if (updateUserResp) {
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.USER.USER_UPDATION_SUCCESS,
          data: updateUserResp,
        };
      }

      return {
        status: RESPONSES.BAD_REQUEST,
        error: true,
        message: RES_MSG.ERROR_MSG.USER_UPDATION_ERROR,
      };
    } catch (error) {
      logger.error(error, 'updateOfferingDetails error');
      if (error.code === 11000) {
        if (error.keyPattern?.['projectDetails.CUSIP']) {
          return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.ERROR_MSG.CUSIP_ERR };
        }
        if (error.keyPattern?.['projectDetails.tokenTicker']) {
          return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.ERROR_MSG.TOKEN_TICKER_ERR };
        }
      }
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * @param {I} data
   * @param {IOffering} filter
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  async createOffering(data: IUpdateOffering): Promise<PromiseResolve> {
    try {
      const query = await offeringSchema.create(data);
      if (query) {
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.USER.USER_UPDATION_SUCCESS,
          data: query,
        };
      }

      return {
        status: RESPONSES.BAD_REQUEST,
        error: true,
        message: RES_MSG.ERROR_MSG.USER_UPDATION_ERROR,
      };
    } catch (error) {
      logger.error(error, 'updateOfferingDetails error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * @param {IOffering} filters
   * @param {any[]} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  async fetchOfferingList(filters: IOffering, projection: any[], pagination: IPagination): Promise<PromiseResolve> {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 }, search } = pagination;
      const skip = (page - 1) * limit;
      const query: FilterQuery<any> = {
        isActive: true,
        isDelete: false,
      };
      if (filters.userId) {
        query.$or = [
          { userId: filters.userId }, // Match userId
          { 'projectDetails.taId': filters.userId?.toString() }, // Match taId
        ];
      }
      if (search) {
        query.$or.push({ title: { $regex: search, $options: 'i' } }, { subTitle: { $regex: search, $options: 'i' } });
      }

      if (filters.status) {
        query.status = filters.status.toUpperCase();
      }

      const totalCount = await offeringSchema.countDocuments(query).exec();
      const users = await offeringSchema.find(query).select(projection).sort(sort).skip(skip).limit(limit).exec();

      const totalPages = Math.ceil(totalCount / limit);

      return {
        data: {
          offering: users,
          currentPage: page,
          totalPages,
          totalCount,
          nextPage: page < totalPages ? page + 1 : null,
          previousPage: page > 1 ? page - 1 : null,
        }
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async getOfferingList(filters: IOffering, projection: any[], pagination: IPagination): Promise<PromiseResolve> {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 }, search } = pagination;
      const skip = (page - 1) * limit;
      const query: FilterQuery<any> = {};

      const addFilter = (key: keyof IOffering, value: any) => {
        if (typeof value !== 'undefined') query[key] = value;
      };

      if (search) {
        query.$or = [{ 'projectDetails.tokenTicker': { $regex: search, $options: 'i' } }, { 'projectDetails.offeringName': { $regex: search, $options: 'i' } }];
      }

      addFilter('isActive', true);
      addFilter('isDelete', false);
      addFilter('status', filters.status);
      addFilter('createdAt', filters.createdAt);

      const totalCount = await offeringSchema.countDocuments(query).exec();
      const pipeline: any = [
        { $match: query }, // Apply the filters to the offerings
        {
          $addFields: {
            taId: {
              $cond: {
                if: { $and: [{ $ifNull: ['$projectDetails.taId', false] }, { $eq: [{ $strLenCP: '$projectDetails.taId' }, 24] }] }
                then: { $toObjectId: '$projectDetails.taId' }
                else: null,
              }
            }
          }
        }
        {
          $lookup: {
            from: 'users', // Ensure collection name is correct
            localField: 'userId',
            foreignField: '_id',
            as: 'userDetails',
          }
        }
        { $unwind: { path: '$userDetails', preserveNullAndEmptyArrays: true } }

        {
          $lookup: {
            from: 'orders',
            localField: '_id', // _id of offerings
            foreignField: 'offeringId', // offeringId in orders
            pipeline: [
              { $match: { status: 'APPROVED' } }, // Count only APPROVED orders
              { $group: { _id: '$offeringId', count: { $sum: 1 } } }, // Count orders per offering
            ],
            as: 'approvedOrderCount',
          }
        }

        // Flatten orderCount to a single value instead of an array
        {
          $addFields: {
            approvedOrderCount: { $ifNull: [{ $arrayElemAt: ['$approvedOrderCount.count', 0] }, 0] }
          }
        }

        // Conditionally join transferagents only if isTransferAgent is true
        {
          $facet: {
            withTransferAgent: [
              { $match: { 'projectDetails.isTransferAgent': true } }, // Check if TransferAgent is required
              {
                $lookup: {
                  from: 'itransferagents',
                  localField: 'taId',
                  foreignField: '_id',
                  as: 'tADetails',
                }
              }
              { $unwind: { path: '$tADetails', preserveNullAndEmptyArrays: true } }
            ],
            withoutTransferAgent: [{ $match: { 'projectDetails.isTransferAgent': false } }],
          }
        }
        {
          $project: {
            data: { $concatArrays: ['$withTransferAgent', '$withoutTransferAgent'] }, // Merge both cases
          }
        }
        { $unwind: '$data' }
        { $replaceRoot: { newRoot: '$data' } }, // Restore the structure
        {
          $project: {
            taId: 1,
            'overview.title': 1,
            'overview.subTitle': 1,
            'overview.icon': 1,
            'overview.logo': 1,
            'overview.cover': 1,
            createdAt: 1,
            'projectDetails.startDate': 1,
            'projectDetails.endDate': 1,
            'projectDetails.assetName': 1,
            'projectDetails.tokenTicker': 1,
            'projectDetails.blockChainType': 1,
            'projectDetails.assetType': 1,
            'projectDetails.offeringName': 1,
            tokenAddress: 1,
            offeringFeeStatus: 1,
            fundAddress: 1,
            erc20Address: 1,
            fee: 1,
            userId: 1,
            status: 1,
            username: '$userDetails.name',
            approvedOrderCount: 1, // Include the order count in the final response
            walletAddress: '$userDetails.wallets.address',
            tADetails: {
              taName: '$tADetails.name',
              taAddress: '$tADetails.walletAddress',
              taEmail: '$tADetails.email',
            }
          }
        }
        { $sort: sort }
        { $skip: skip }
        { $limit: limit }
      ];

      const users = await offeringSchema.aggregate(pipeline).exec();
      const totalPages = Math.ceil(totalCount / limit);

      return {
        data: {
          offering: users,
          currentPage: page,
          totalPages,
          totalCount,
          nextPage: page < totalPages ? page + 1 : null,
          previousPage: page > 1 ? page - 1 : null,
        }
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      logger.error(error);
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * Generates an offering report based on specified search details including offering IDs and period.
   * It organizes data into time frames (hourly, daily, monthly, or yearly) and calculates cumulative balances.
   *
   * @param {Object} searchDetails - An object containing the offering ID(s) and the time period.
   * @returns {Promise<PromiseResolve>} - A Promise that resolves with the formatted offering data.
   *
   * Function Details:
   * - Extracts the offering ID and period from the searchDetails input.
   * - Determines start and end dates for the specified period using CommonHelper.getDateRange.
   * - Builds appropriate groupings and time frames for the report based on the period:
   *   - For yearly periods: Groups data by year and month.
   *   - For monthly or shorter periods (15 days, 7 days): Groups data by day.
   *   - For a single-day period: Groups data by hour.
   * - Constructs an aggregation pipeline using MongoDB features:
   *   - Matches data within the specified time range and with a status of 'MINTED'.
   *   - Groups and calculates total amounts based on group criteria (year, month, day, hour).
   *   - Sorts data by time-related fields for correct ordering.
   *   - Handles data prior to the start date to calculate a cumulative starting balance.
   * - Processes the main data and maps it against time frames to ensure all time intervals have data,
   *   filling missing intervals with zeros for continuity.
   * - Accumulates cumulative balances over the time frames.
   * - Formats the output to include offering IDs and their respective charts.
   *
   * Key Error Handling:
   * - Throws a `CustomError` for invalid period values.
   * - Handles and logs any unexpected errors, returning a standardized response.
   */

  async offeringReport(searchDetails: any): Promise<PromiseResolve> {
    try {
      const { offeringId, period } = searchDetails;
      const { startDate, endDate } = CommonHelper.getDateRange(period);

      let groupBy: any;
      let timeFrames: any[] = [];
      if (['1Y', '2Y', '3Y'].includes(period)) {
        groupBy = { year: { $year: '$createdAt' }, month: { $month: '$createdAt' } };
        timeFrames = Array.from({ length: 12 }, (_, i) => ({
          month: i + 1,
          groupStartTime: moment.unix(startDate).startOf('month').add(i, 'months').toISOString(),
          groupEndTime: moment
            .unix(startDate)
            .startOf('month')
            .add(i + 1, 'months')
            .subtract(1, 'second')
            .toISOString(),
        }));
      } else if (['1M', '15D', '7D'].includes(period)) {
        const daysInRange = moment.unix(endDate).diff(moment.unix(startDate), 'days') + 1;
        groupBy = { year: { $year: '$createdAt' }, month: { $month: '$createdAt' }, day: { $dayOfMonth: '$createdAt' } };
        timeFrames = Array.from({ length: daysInRange }, (_, i) => {
          const date = moment.unix(startDate).add(i, 'days');

          return { day: date.date(), month: date.month() + 1, groupStartTime: date.startOf('day').toISOString(), groupEndTime: date.endOf('day').toISOString() };
        });
      } else if (period === '1D') {
        groupBy = { year: { $year: '$createdAt' }, month: { $month: '$createdAt' }, day: { $dayOfMonth: '$createdAt' }, hour: { $hour: '$createdAt' } };
        timeFrames = Array.from({ length: 24 }, (_, i) => ({
          hour: i,
          groupStartTime: moment.unix(startDate).startOf('day').add(i, 'hours').toISOString(),
          groupEndTime: moment
            .unix(startDate)
            .startOf('day')
            .add(i + 1, 'hours')
            .subtract(1, 'second')
            .toISOString(),
        }));
      } else {
        throw new CustomError('Invalid period specified', RESPONSES.BAD_REQUEST);
      }

      const pipeline: any = [
        {
          $facet: {
            // Main data pipeline
            mainData: [
              {
                $match: {
                  offeringId: { $in: offeringId.map((id: any) => new Types.ObjectId(id)) }
                  createdAt: { $gte: new Date(startDate * 1000), $lte: new Date(endDate * 1000) }
                  status: {
                    $in: [
                      'MINTED',
                      //  'BURN', 'REDEEM', 'TRANSFER_FROM', 'TRANSFER_TO', 'CONVERT_TO', 'CONVERT_FROM'
                    ],
                  }
                }
              }
              {
                $addFields: {
                  quantity: { $toDecimal: '$quantity' }
                  amount: { $toDecimal: '$amount' }
                  feesInPercentage: { $toDecimal: '$feesInPercentage' }
                  price: { $toDecimal: '$price' }
                  wap: { $toDecimal: '$wap' }
                }
              }
              {
                $group: {
                  _id: groupBy,
                  offeringId: { $first: '$offeringId' }
                  currentQuantity: {
                    $sum: {
                      $switch: {
                        branches: [
                          { case: { $eq: ['$status', 'MINTED'] }, then: '$quantity' }
                          // { case: { $eq: ['$status', 'TRANSFER_TO'] }, then: '$quantity' }
                          // { case: { $eq: ['$status', 'CONVERT_TO'] }, then: '$quantity' }
                          // { case: { $in: ['$status', ['BURN', 'REDEEM', 'TRANSFER_FROM', 'CONVERT_FROM']] }, then: { $multiply: ['$quantity', -1] } }
                        ],
                        default: 0,
                      }
                    }
                  }
                  totalAmount: {
                    $sum: {
                      $switch: {
                        branches: [
                          { case: { $eq: ['$status', 'MINTED'] }, then: '$amount' }
                          // { case: { $eq: ['$status', 'TRANSFER_TO'] }, then: '$quantity' }
                          // { case: { $eq: ['$status', 'CONVERT_TO'] }, then: '$quantity' }
                          // { case: { $in: ['$status', ['BURN', 'REDEEM', 'TRANSFER_FROM', 'CONVERT_FROM']] }, then: { $multiply: ['$quantity', -1] } }
                        ],
                        default: 0,
                      }
                    }
                  }
                }
              }
              { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1, '_id.hour': 1 } }
            ],

            // Total Before Start Date pipeline
            totalBeforeStartDate: [
              {
                $match: {
                  offeringId: { $in: offeringId.map((id: any) => new Types.ObjectId(id)) }
                  createdAt: { $lt: new Date(startDate * 1000) }, // This is the condition
                  status: {
                    $in: [
                      'MINTED',
                      //  'BURN', 'REDEEM', 'TRANSFER_FROM', 'TRANSFER_TO', 'CONVERT_TO', 'CONVERT_FROM'
                    ],
                  }
                }
              }
              {
                $addFields: {
                  quantity: { $toDecimal: '$quantity' }
                  amount: { $toDecimal: '$amount' }
                  feesInPercentage: { $toDecimal: '$feesInPercentage' }
                  currentPrice: { $toDecimal: '$currentPrice' }
                  wap: { $toDecimal: '$wap' }
                }
              }
              {
                $group: {
                  _id: null,
                  currentQuantityBeforeStartDate: {
                    $sum: {
                      $switch: {
                        branches: [
                          { case: { $eq: ['$status', 'MINTED'] }, then: '$quantity' }
                          // { case: { $eq: ['$status', 'TRANSFER_TO'] }, then: '$quantity' }
                          // { case: { $eq: ['$status', 'CONVERT_TO'] }, then: '$quantity' }
                          // { case: { $in: ['$status', ['BURN', 'REDEEM', 'TRANSFER_FROM', 'CONVERT_FROM']] }, then: { $multiply: ['$quantity', -1] } }
                        ],
                        default: 0,
                      }
                    }
                  }
                  totalAmountBeforeStartDate: {
                    $sum: {
                      $switch: {
                        branches: [
                          { case: { $eq: ['$status', 'MINTED'] }, then: '$amount' }
                          // { case: { $eq: ['$status', 'TRANSFER_TO'] }, then: '$quantity' }
                          // { case: { $eq: ['$status', 'CONVERT_TO'] }, then: '$quantity' }
                          // { case: { $in: ['$status', ['BURN', 'REDEEM', 'TRANSFER_FROM', 'CONVERT_FROM']] }, then: { $multiply: ['$quantity', -1] } }
                        ],
                        default: 0,
                      }
                    }
                  }
                }
              }
            ],
          }
        }
        // Unwind totalBeforeStartDate to make it consistent
        {
          $unwind: {
            path: '$totalBeforeStartDate',
            preserveNullAndEmptyArrays: true, // Ensures null values are preserved
          }
        }
        { $project: { mainData: 1, cumulativeBalanceBeforeStartDate: '$totalBeforeStartDate.totalAmountBeforeStartDate' } }
      ];
      const results = await OrderSchema.aggregate(pipeline);
      const { mainData, cumulativeBalanceBeforeStartDate } = results[0];
      console.log('first', { mainData, cumulativeBalanceBeforeStartDate });
      const formattedResult = offeringId.map((id: any) => {
        const groupedResults = mainData.filter((item: any) => item.offeringId.toString() === id);
        const chart: any = timeFrames.map((timeFrame) => {
          const match = groupedResults.find((item: any) => {
            const group = item._id;
            if (['1Y', '2Y', '3Y'].includes(period)) {
              return group.month === timeFrame.month;
            }
            if (['1M', '15D', '7D'].includes(period)) {
              return group.day === timeFrame.day && group.month === timeFrame.month;
            }
            if (period === '1D') {
              return group.hour === timeFrame.hour;
            }

            return false;
          });

          return {
            totalAmount: match ? match.totalAmount : 0,
            groupStartTime: timeFrame.groupStartTime,
            groupEndTime: timeFrame.groupEndTime,
            ...(period === '1Y' && { month: timeFrame.month }),
            ...(period === '1D' && { hour: timeFrame.hour }),
            ...(['1M', '15D', '7D'].includes(period) && { day: timeFrame.day, month: timeFrame.month }),
          };
        });

        const updatedChart: any = chart.reduce((acc: any[], item: any, index: number) => {
          const previousBalance = acc.length > 0 ? acc[acc.length - 1].cumulativeBalance || 0 : 0;
          if (index === 0) {
            item = { ...item, cumulativeBalance: cumulativeBalanceBeforeStartDate ? calculate('add', cumulativeBalanceBeforeStartDate, item.totalAmount) : item.totalAmount };
          } else if (item.totalAmount > 0) {
            item = { ...item, cumulativeBalance: calculate('add', previousBalance, item.totalAmount) };
          } else if (previousBalance > 0) {
            item = { ...item, cumulativeBalance: previousBalance };
          } else {
            item = { ...item, cumulativeBalance: 0 };
          }

          acc.push(item);

          return acc;
        }, []);

        return { offeringId: id, chart: updatedChart };
      });

      return { status: RESPONSES.SUCCESS, error: false, message: 'Records fetched successfully', data: formattedResult };
    } catch (error) {
      logger.error('offeringReport', error);

      return { status: RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || 'Internal server error' };
    }
  }
  async updateWhitelistDetails(data: IUpdateWhitelist, filter: FilterQuery<IOffering>): Promise<PromiseResolve> {
    try {
      const updateUserResp = await whitelistSchema.findOneAndUpdate(filter, data, {
        new: true,
        runValidators: true,
        //  upsert: true,
      });
      const offering = await offeringSchema.findOne({ _id: data?.offeringId });
      const userDetails = await userSchema.findOne({ _id: updateUserResp?.userId });
      const { name } = userDetails;
      const { offeringName } = offering?.projectDetails;
      let taId = offering?.projectDetails?.taId;
      const taemail = await transferAgentSchema.findOne({ _id: taId });
      const { email } = taemail;
      const format = {
        offeringId: filter,
        userId: updateUserResp._id,
        // userId: '67d8f655c3391fe4577839b1',
        status: data.status,
        txHash: updateUserResp?.txHash,
      };
      taId = '67b57e1c50b0155a97072edb';
      if (taId) {
        // Emit the data using taId
        socketHelper.sendMessageToUser(taId, 'updateWhiteList', format);
      }
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USER_UPDATION_SUCCESS,
        data: updateUserResp,
      };
    } catch (error) {
      logger.error(error, 'updateWhitelistDetails error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async createWhitelistDetails(data: IUpdateWhitelist, filter: FilterQuery<IOffering>): Promise<PromiseResolve> {
    try {
      // const updateUserResp = await whitelistSchema.create(data);
      const updateUserResp = await whitelistSchema.findOneAndUpdate(filter, data, {
        new: true,
        runValidators: true,
        upsert: true,
      });
      const offering = await offeringSchema.findOne({ _id: updateUserResp?.offeringId });
      const taId = offering?.projectDetails?.taId;
      if (taId) {
        const taDetail = await transferAgentSchema.findOne({ _id: taId });
        const investordetail = await userSchema.findOne({ _id: updateUserResp?.userId });
        const investorWalletAddress = investordetail.wallets[0].address;
        const detail = {
          investorName: investordetail?.name,
          issuerName: taDetail?.name,
          offeringName: offering?.projectDetails?.offeringName,
          investorEmail: investordetail?.email,
          offeringId: updateUserResp?.offeringId,
          investorWalletAddress,
        };
        emailHelper.sendEmailTemplate(taDetail?.email, 'walletWhiteListRequest', detail);
      }
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USER_UPDATION_SUCCESS,
        data: updateUserResp,
      };
    } catch (error) {
      logger.error(error, 'updateWhitelistDetails error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async updateorderDetails(data: IUpdateOrder, filter: FilterQuery<IOffering>): Promise<PromiseResolve> {
    try {
      const updateUserResp = await OrderSchema.findOneAndUpdate(filter, data, {
        new: true,
        runValidators: true,
        upsert: true,
      });
      const offering = await offeringSchema.findOne({ _id: updateUserResp?.offeringId });
      let taId = offering?.projectDetails?.taId;
      if (taId) {
        // Emit the data using taId
        socketHelper.sendMessageToUser(taId, 'updateTranscation', updateUserResp);
      }

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USER_UPDATION_SUCCESS,
        data: updateUserResp,
      };
    } catch (error) {
      logger.error(error, 'updateWhitelistDetails error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async dashboardList(filters: IOffering, projection: any[], pagination: IPagination): Promise<PromiseResolve> {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 }, search } = pagination;
      const skip = (page - 1) * limit;
      const query: FilterQuery<any> = {};

      const addFilter = (key: keyof IOffering, value: any) => {
        if (typeof value !== 'undefined') query[key] = value;
      };
      query.status = { $nin: ['REJECTED', 'IN_PROGRESS'] };

      if (search) {
        query.$or = [{ title: { $regex: search, $options: 'i' } }, { subTitle: { $regex: search, $options: 'i' } }];
      }

      addFilter('isActive', true);
      addFilter('status', filters.status);
      addFilter('userId', filters.userId);

      const totalCount = await offeringSchema.countDocuments(query).exec();
      const pendingCount = await offeringSchema.countDocuments({
        status: 'PENDING',
      });
      const rejectedCount = await offeringSchema.countDocuments({
        status: 'REJECTED',
      });

      const users = await offeringSchema.find(query).select(projection).sort(sort).skip(skip).limit(limit).exec();

      const totalPages = Math.ceil(totalCount / limit);

      return {
        data: {
          offering: users,
          currentPage: page,
          totalPages,
          totalCount,
          pendingCount,
          rejectedCount,
          nextPage: page < totalPages ? page + 1 : null,
          previousPage: page > 1 ? page - 1 : null,
        }
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * @param {I} data
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  async requestOffering(data: IRequestedOffering, filter: FilterQuery<IRequestedOffering>): Promise<PromiseResolve> {
    try {
      const search = { _id: filter?._id };
      const query = await requestedOfferingSchema.findByIdAndUpdate(search, data, {
        new: true,
        runValidators: true,
        upsert: true,
      });
      if (query) {
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.COMMON.CREATED_SUCCESS,
          data: query,
        };
      }

      return {
        status: RESPONSES.BAD_REQUEST,
        error: true,
        message: RES_MSG.ERROR_MSG.USER_UPDATION_ERROR,
      };
    } catch (error) {
      logger.error(error, 'requestOffering error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * @param {I} filters
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */

  async requestedOfferings(filters: any, pagination: IPagination): Promise<PromiseResolve> {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 }, search } = pagination;
      const skip = (page - 1) * limit;
      const query: FilterQuery<any> = {};
      if (filters.status) query['status'] = filters.status;
      if (filters.subAdminId) query['subAdminId'] = filters.subAdminId;
      // if (search) {
      //   const searchRegex = new RegExp(search, 'i');
      //   query.$or = [
      //     { 'userDetails.name': { $regex: searchRegex } }
      //     { 'userDetails.email': { $regex: searchRegex } }
      //     { 'subAdminDetails.name': { $regex: searchRegex } }
      //     { 'subAdminDetails.email': { $regex: searchRegex } }
      //   ];
      // }
      const pipeline: any = [
        { $match: query }
        {
          $lookup: {
            from: 'users', // Lookup subAdmin details
            localField: 'subAdminId',
            foreignField: '_id',
            as: 'subAdminDetails',
          }
        }
        { $unwind: { path: '$subAdminDetails', preserveNullAndEmptyArrays: true } }
        {
          $lookup: {
            from: 'users', // Lookup user details
            localField: 'userId',
            foreignField: '_id',
            as: 'userDetails',
          }
        }
        { $unwind: { path: '$userDetails', preserveNullAndEmptyArrays: true } }
        {
          $addFields: {
            subAdminEmail: {
              $cond: {
                if: { $regexMatch: { input: '$subAdminDetails.email', regex: search || '', options: 'i' } }, // Match subAdminEmail
                then: '$subAdminDetails.email',
                else: null,
              }
            }
            subAdminName: {
              $cond: {
                if: { $regexMatch: { input: '$subAdminDetails.email', regex: search || '', options: 'i' } }
                then: '$subAdminDetails.name',
                else: null,
              }
            }
          }
        }
        {
          $project: {
            _id: 1,
            createdAt: 1,
            status: 1,
            updatedAt: 1,
            userId: 1,
            subAdminId: 1,
            userName: '$userDetails.name',
            userEmail: '$userDetails.email',
            userImage: '$userDetails.userImage',
            subAdminName: 1,
            subAdminEmail: 1,
            subAdminImage: '$subAdminDetails.userImage',
          }
        }
        { $sort: sort }
        { $skip: skip }
        { $limit: limit }
      ];

      const totalCount = await requestedOfferingSchema.countDocuments(query).exec();
      let offerings = await requestedOfferingSchema.aggregate(pipeline).exec();
      // const totalPages = Math.ceil(totalCount / limit);
      if (search) {
        const searchRegex = new RegExp(search, 'i');
        offerings = offerings.filter((offering: any) => {
          const userNameMatch = offering.userName && offering.userName.match(searchRegex);
          const userEmailMatch = offering.userEmail && offering.userEmail.match(searchRegex);
          const subAdminNameMatch = offering.subAdminName && offering.subAdminName.match(searchRegex);
          const subAdminEmailMatch = offering.subAdminEmail && offering.subAdminEmail.match(searchRegex);

          return userNameMatch || userEmailMatch || subAdminNameMatch || subAdminEmailMatch;
        });
      }

      // Recalculate total count after filtering by search
      const filteredCount = offerings.length;

      // Calculate total pages after applying search
      const totalPages = Math.ceil(filteredCount / limit);

      // Calculate pagination details
      return {
        data: {
          offering: offerings,
          currentPage: page,
          totalPages,
          totalCount: filteredCount, // Return filtered count as totalCount
          nextPage: page < totalPages ? page + 1 : null,
          previousPage: page > 1 ? page - 1 : null,
        }

        // return {
        //   data: {
        //     offering: offerings,
        //     currentPage: page,
        //     totalPages,
        //     totalCount,
        //     nextPage: page < totalPages ? page + 1 : null,
        //     previousPage: page > 1 ? page - 1 : null,
        //   }
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      logger.error(error, 'requestedOfferings error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }
  async requestedOfferingsCsv(filters: any): Promise<PromiseResolve> {
    try {
      const query: FilterQuery<any> = {};

      // Apply filters for status and subAdminId
      if (filters.status) query['status'] = filters.status;
      if (filters.subAdminId) query['subAdminId'] = filters.subAdminId;

      const pipeline: any = [
        { $match: query }
        {
          $lookup: {
            from: 'users', // Lookup subAdmin details
            localField: 'subAdminId',
            foreignField: '_id',
            as: 'subAdminDetails',
          }
        }
        { $unwind: { path: '$subAdminDetails', preserveNullAndEmptyArrays: true } }
        {
          $lookup: {
            from: 'users', // Lookup user details
            localField: 'userId',
            foreignField: '_id',
            as: 'userDetails',
          }
        }
        { $unwind: { path: '$userDetails', preserveNullAndEmptyArrays: true } }
        {
          $addFields: {
            subAdminEmail: '$subAdminDetails.email',
            subAdminName: '$subAdminDetails.name',
          }
        }
        {
          $project: {
            _id: 1,
            createdAt: 1,
            status: 1,
            updatedAt: 1,
            userId: 1,
            subAdminId: 1,
            userName: '$userDetails.name',
            userEmail: '$userDetails.email',
            userImage: '$userDetails.userImage',
            subAdminName: 1,
            subAdminEmail: 1,
            subAdminImage: '$subAdminDetails.userImage',
          }
        }
      ];

      // Fetch all requested offerings based on filters without pagination
      const offerings = await requestedOfferingSchema.aggregate(pipeline).exec();
      const totalCount = offerings.length; // Total count is simply the length of offerings

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
        data: {
          offering: offerings,
        }
      };
    } catch (error) {
      logger.error(error, 'requestedOfferings error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * Adds a country to the authorizedCountries list of an offering.
   * @param complianceData - Data containing compliance, countryCode, name, and isoCode
   * @returns Promise<PromiseResolve>
   */
  async countryAllow(complianceData: any): Promise<PromiseResolve> {
    try {
      const { compliance, countryCode, name, isoCode } = complianceData;

      // Validate input data
      if (!compliance || !countryCode || !name || !isoCode) {
        throw new CustomError(RES_MSG.COMMON.BAD_REQUEST, RESPONSES.BAD_REQUEST);
      }

      // Check for duplicate country by isoCode
      const existing = await offeringSchema.findOne({
        compliance,
        'projectDetails.authorizedCountries.isoCode': isoCode,
      });
      if (existing) {
        throw new CustomError('Country already authorized', RESPONSES.BAD_REQUEST);
      }

      const result = await offeringSchema.findOneAndUpdate(
        { compliance }
        {
          $addToSet: {
            'projectDetails.authorizedCountries': {
              name,
              isoCode,
              countryCode,
            }
          }
        }
        { new: true, runValidators: true }
      );

      if (!result) {
        throw new CustomError(RES_MSG.COMMON.NO_OFFERING, RESPONSES.NOTFOUND);
      }

      return {
        message: 'Country added successfully',
        status: RESPONSES.ACCEPTED,
        error: false,
        data: result,
      };
    } catch (error) {
      logger.error('countryAllow', error);
      return {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      };
    }
  }
}

export default new OfferingService();
