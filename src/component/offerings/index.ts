import { Request, Response } from 'express';
import { offeringStatusEnum, PromiseResolve, UserType } from '../../utils/common.interface';
import OfferingService from './service';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import CustomError from '../../helpers/customError.helper';
import { ResponseHandler } from '../../helpers/response.helper';
import logger from '../../helpers/logging/logger.helper';
import { IOffering } from './models/offerings.model';
import CommonHelper from '../../helpers/common.helper';
import mongoose, { Types } from 'mongoose';
import UserService from '../userAuthentications/service';
import { offeringDocs } from '../../utils/constant';
import userClient from '../../_grpc/clients/user.client';
import { transferAgentSchema } from '../transferagent/transferagent.model';
import { requestedOfferingSchema } from './models/requestedOfferings.model';
import offeringValidation from '../../component/offerings/validation';
import emailHelper from '../../helpers/email.helper';
import { kafkaHelperService } from '../../helpers/kafka.helper';
import kafkaService from '../../services/kafkaService';
import { permissionSchema } from '../subadmin/permission.model';
import { userSchema } from '../userAuthentications/user.model';
/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise < PromiseResolve >}
 */
export async function getOfferingDetailsController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const offeringId: any = req.query.offerId;
    const searchQuery = { _id: offeringId };
    let formattedDocuments;
    const offeringDetails: PromiseResolve = await OfferingService.fetchOfferingDetails(searchQuery, [], [], true);
    if (offeringDetails.error) {
      throw new CustomError(offeringDetails.message, offeringDetails.status);
    }

    const data: IOffering | any = offeringDetails.data;
    const { _id, overview, projectDetails, documents, team, currentStep, isActive, status, isTokenDeploy, tokenAddress, fundAddress, isFundDeploy } = data;
    const taId = projectDetails?.taId;
    let taWalletAddress;
    if (taId) {
      const taDetails = await transferAgentSchema.findOne({ _id: taId }, 'walletAddress name email');
      taWalletAddress = taDetails || {};
    } else {
      taWalletAddress = {};
    }

    // if (status === 'REJECTED') {
    //   throw new CustomError(RES_MSG.SUCCESS_MSG.NO_OFFERING, RESPONSES.NOTFOUND);
    // }
    const getFileType = (url: string): string => {
      const extension = url.split('.').pop()?.toLowerCase();
      return extension;
    };

    if (documents) {
      formattedDocuments = offeringDocs.map((doc) => {
        const url = documents[doc?.name];
        if (url) {
          return {
            title: doc.title,
            url: url || null,
            type: url ? getFileType(url) : 'N/A',
          };
        }
      });

      if (documents.customDocs && documents.customDocs.length > 0) {
        documents.customDocs.forEach((customDoc: { docsLabel: string; value: string }) => {
          formattedDocuments.push({
            title: customDoc.docsLabel,
            url: customDoc.value,
            type: getFileType(customDoc.value),
          });
        });
      }
    }

    let cleanedDocuments = formattedDocuments?.filter(function (doc: any) {
      return doc !== null && doc !== undefined;
    });
    const profileUser = {
      _id,
      overview,
      projectDetails,
      taWalletAddress,
      documents: cleanedDocuments,
      team,
      currentStep,
      isActive,
      status,
      isTokenDeploy,
      tokenAddress,
      fundAddress,
      isFundDeploy,
      document: documents,
    };
    return ResponseHandler.success(res, {
      status: offeringDetails.status || RESPONSES.SUCCESS,
      error: false,
      message: offeringDetails.message || RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
      data: profileUser,
    });
  } catch (error: any) {
    logger.error(error, 'getOffering Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise < PromiseResolve >}
 */
export async function rejectOffering(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    console.log('i am in reject offering');
    const { offeringId, reason } = req.body;
    const searchQuery = { _id: offeringId };
    // Fetch offering details
    const offeringDetails: PromiseResolve = await OfferingService.fetchOfferingDetails(searchQuery, [], [], true);
    const _id = offeringDetails?.data?.createdBy;
    const date = offeringDetails?.data?.updatedAt;
    const offeringName = offeringDetails?.data?.projectDetails?.offeringName;
    const data: any = await UserService.fetchUserDetails({ _id });
    const { email, name }: any = data.data;
    if (offeringDetails.error) {
      throw new CustomError(offeringDetails.message, offeringDetails.status);
    }

    // Prepare update data
    const updateData: any = {
      status: offeringStatusEnum.REJECTED,
      reason,
    };

    // send reject offering to kafka notification topic
    await kafkaService.sendMessageToNotification({
      value: {
        type: 'offering-rejected',
        details: {
          offeringId,
          reason,
          status: offeringStatusEnum.REJECTED,
        },
      },
    });

    // gRPC call to reject offering
    const payload = { id: offeringId, status: offeringStatusEnum.REJECTED, reason };
    userClient.client.rejectOffering(payload, async (error: any, response: any) => {
      if (error) {
        logger.error('gRPC Error:', error);
        return ResponseHandler.error(res, {
          status: RESPONSES.INTERNAL_SERVER_ERROR,
          error: true,
          message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        });
      }

      if (response.error) {
        return ResponseHandler.error(res, {
          message: response.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
          status: RESPONSES.BAD_REQUEST,
          error: true,
        });
      }

      // If gRPC response is successful, update offering details
      await OfferingService.updateOfferingDetails(updateData, { _id: offeringId });
      const emailDetail = {
        email,
        name,
        offeringId,
        date,
        reason,
        offeringName,
      };
      emailHelper.sendEmailTemplate(email, 'rejectoffering', emailDetail);
      // Send success response
      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.OFFERING_REJECT || RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: '',
      });
    });
  } catch (error: any) {
    logger.error(error, 'rejectOffering Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise < PromiseResolve >}
 */

export async function offeringReportController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { offeringId, period } = req.body;

    await Promise.all(
      offeringId.map(async (id: string) => {
        const response: PromiseResolve = await OfferingService.fetchOfferingDetails({ _id: new Types.ObjectId(id) });

        if (response.error) throw new CustomError(response.message, response.status);
      }),
    );

    const result: PromiseResolve = await OfferingService.offeringReport({ offeringId, period });
    if (result.error) throw new CustomError(result.message, result.status);

    return ResponseHandler.success(res, { status: result.status || RESPONSES.SUCCESS, error: false, message: result.message || RES_MSG.USER.USERS_FETCH, data: result.data || [] });
  } catch (error) {
    logger.error(error, 'offeringReport Error');

    return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
  }
}

export async function createOfferingController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { userId: subAdminId, email } = req.userInfo;
    const filteredPermissions = await permissionSchema.aggregate([
      {
        $match: {
          userId: new mongoose.Types.ObjectId(subAdminId),
        },
      },
      {
        $project: {
          permission: {
            $filter: {
              input: '$permission',
              as: 'perm',
              cond: {
                $or: [{ $eq: ['$$perm.moduelsId', new mongoose.Types.ObjectId('672c70ce05e65803246ffc44')] }, { $eq: ['$$perm.moduelsId', new mongoose.Types.ObjectId('672c70e305e65803246ffc45')] }],
              },
            },
          },
        },
      },
    ]);

    const perms = filteredPermissions[0]?.permission || [];

    const hasInvalidPermission = perms.some((p: { read: any; moduelsId: { toString: () => string }; write: any }) => !p.read || (p.moduelsId.toString() === '672c70ce05e65803246ffc44' && !p.write));

    console.log('Filtered Permissions:', JSON.stringify(filteredPermissions));

    if (hasInvalidPermission) {
      throw new CustomError(RES_MSG.USER.UNAUTHORIZE, RESPONSES.CONFLICT);
    }
    const { offeringId: offeringIdRaw, userId, ...offeringDetails } = req.body;
    const offeringId = offeringIdRaw ? new Types.ObjectId(offeringIdRaw) : null;
    const isAssigned = await OfferingService.fetchAssignedOfferingDetails({ _id: offeringId, subAdminId: new Types.ObjectId(subAdminId) });
    if (isAssigned.error) {
      throw new CustomError(isAssigned.message, isAssigned.status);
    }
    let updateOfferingResp: PromiseResolve, currentOfferingResp: PromiseResolve;

    if (offeringId) {
      // Fetch current offering if offeringId exists
      currentOfferingResp = await OfferingService.fetchOfferingDetails({ _id: offeringId, userId });
      if (currentOfferingResp.error && currentOfferingResp.status === RESPONSES.NOTFOUND) {
        // If no offeringId, insert new offering
        updateOfferingResp = await OfferingService.createOffering({
          ...offeringDetails,
          userId,
          currentStep: 0,
          createdBy: subAdminId,
          _id: offeringId,
        });
        await OfferingService.requestOffering({ status: offeringStatusEnum.IN_PROGRESS }, { _id: offeringId });
        if (updateOfferingResp.error) {
          throw new CustomError(updateOfferingResp.message || RES_MSG.ERROR_MSG.UPDATE_FAILED, updateOfferingResp.status);
        }
        return ResponseHandler.success(res, {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.SUCCESS_MSG.OFFERING_CREATE_SUCCESS,
          data: updateOfferingResp.data,
        });
      }
      const { data: currentOfferingData } = currentOfferingResp;
      const { currentStep = 0, status } = currentOfferingData;
      const requestedStep = offeringDetails?.currentStep || 0;
      let updateData = { ...offeringDetails };
      // Step-based validation
      if (requestedStep === currentStep + 1) {
        updateData.currentStep = requestedStep;
      } else if (requestedStep <= currentStep) {
        delete updateData.currentStep;
      } else {
        return ResponseHandler.error(res, {
          status: RESPONSES.BAD_REQUEST,
          error: true,
          message: `Invalid step progression. Current step: ${currentStep}. Proceed to step ${currentStep + 1}.`,
        });
      }
      // Status validations
      if (status === offeringStatusEnum.APPROVED) {
        throw new CustomError(RES_MSG.USER.ALREADY_APPROVED_OFFERING, RESPONSES.CONFLICT);
      }
      if (status === offeringStatusEnum.PENDING) {
        throw new CustomError(RES_MSG.USER.ALREADY_PENDING_OFFERING, RESPONSES.CONFLICT);
      }

      if (status === offeringStatusEnum.REVIEW) {
        throw new CustomError(RES_MSG.USER.ALREADY_REVIEW_OFFERING, RESPONSES.CONFLICT);
      }
      // Merge and validate fields
      const { projectDetails } = offeringDetails;
      const { projectDetails: currentProjectDetails = {} } = currentOfferingData;

      if (projectDetails?.isPrivate && projectDetails?.offeringMembers) {
        projectDetails.offeringMembers = CommonHelper.mergeWithoutDuplicates(projectDetails.offeringMembers, currentProjectDetails.offeringMembers || []);
      }

      if (projectDetails?.offeringMembers?.includes(email)) {
        throw new CustomError(RES_MSG.ERROR_MSG.OFFERING_OWN_EMAIL, RESPONSES.BAD_REQUEST);
      }
      // if (offeringDetails?.team) {
      //   offeringDetails.team = CommonHelper.mergeWithoutDuplicates(
      //     offeringDetails.team,
      //     currentOfferingData.team || [],
      //     ['email']
      //   );
      // }

      // if (offeringDetails?.documents?.customDocs) {
      //   offeringDetails.documents.customDocs = CommonHelper.mergeWithoutDuplicates(
      //     offeringDetails.documents.customDocs,
      //     currentOfferingData.documents?.customDocs || [],
      //     ['docsLabel']
      //   );
      // }

      if (projectDetails?.customFields) {
        projectDetails.customFields = CommonHelper.mergeWithoutDuplicates(projectDetails.customFields, currentProjectDetails.customFields || [], ['label', 'type']);
      }

      if (offeringDetails?.documents) {
        req.body.documents.assetType = currentProjectDetails.assetType;
        const validateRequest = await offeringValidation.createOfferingsValidation({ ...req.body });
        if (validateRequest.error) {
          throw new CustomError(validateRequest.message, validateRequest.status);
        }
        offeringDetails.documents = validateRequest.value.documents;
      }
      if (projectDetails && !projectDetails?.isTransferAgent) {
        projectDetails.taId = '';
      }

      // Update the offering details
      updateOfferingResp = await OfferingService.updateOfferingDetails(updateData, { _id: offeringId });
      await OfferingService.requestOffering({ status: offeringStatusEnum.IN_PROGRESS }, { _id: offeringId });
      if (updateOfferingResp.error) {
        throw new CustomError(updateOfferingResp.message || RES_MSG.ERROR_MSG.UPDATE_FAILED, updateOfferingResp.status);
      }
      if (offeringDetails.isFinalSubmission) {
        updateOfferingResp = await OfferingService.updateOfferingDetails({ status: offeringStatusEnum.REVIEW }, { _id: offeringId });
        await new Promise((resolve, reject) => {
          userClient.client.sendOffering({ data: JSON.stringify(updateOfferingResp.data) }, async (error: any, response: PromiseResolve) => {
            if (error || response?.error) {
              const errorMessage = error?.message || response?.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR;
              const errorStatus = error?.status || RESPONSES.INTERNAL_SERVER_ERROR;
              await OfferingService.updateOfferingDetails({ status: offeringStatusEnum.IN_PROGRESS, isFinalSubmission: false, currentStep: 4 }, { _id: offeringId });
              logger.error('gRPC Error:', error || response);
              return reject(new CustomError(errorMessage, errorStatus));
            }

            await OfferingService.requestOffering({ status: offeringStatusEnum.REVIEW }, { _id: offeringId });
            resolve(response);
          });
        });
      }
    }

    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.SUCCESS_MSG.OFFERING_CREATE_SUCCESS,
      data: updateOfferingResp.data,
    });
  } catch (error) {
    logger.error(error, 'createOffering Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise < PromiseResolve >}
 */
export async function getOfferingListController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { page = 1, limit = 10, sort, userId = '', status = '', search = '' } = req.query;

    if (userId && !Types.ObjectId.isValid(userId as string)) {
      return ResponseHandler.error(res, {
        message: RES_MSG.COMMON.NO_FOUND,
        status: RESPONSES.BAD_REQUEST,
        error: true,
      });
    }
    const userIdObj = userId ? new Types.ObjectId(userId as string) : null;
    const filters = {
      ...(status && { status: status }),
      ...(userIdObj && { userId: userIdObj }),
    };
    const userDetails: PromiseResolve = await UserService.fetchUserDetails({ _id: userIdObj }, ['name', 'email', 'userImage'], ['_id']);
    const projection = [
      'overview.title',
      'overview.subTitle',
      'overview.icon',
      'overview.logo',
      'overview.cover',
      'projectDetails.assetName',
      'projectDetails.blockChainType',
      'projectDetails.assetType',
      'projectDetails.offeringName',
      'tokenAddress',
      'fundAddress',
      'offeringFeeStatus',
      'createdAt',
    ];
    const offeringList: PromiseResolve = await OfferingService.fetchOfferingList(filters, projection, {
      page: Number(page),
      limit: Number(limit),
      ...(search && { search: search }),
      ...(sort && { sort: JSON.parse(sort as string) }),
    });

    return ResponseHandler.success(res, {
      status: offeringList.status || RESPONSES.SUCCESS,
      error: false,
      message: offeringList.message || RES_MSG.USER.USERS_FETCH,
      data: {
        user: userDetails.data,
        ...offeringList.data,
      },
    });
  } catch (error) {
    logger.error(error, 'fetch Offering List Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function getOfferingList(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { page = 1, limit = 10, sort, userId = '', status = '', search = '', startDate = '', endDate = '' } = req.query;
    const disallowedSymbolsRegex = /[!+#$%^*(),?":{}|;<>]/;

    // Validate the search parameter using the CommonHelper's validateStringRegex
    const validateSearchResult = await CommonHelper.validateStringRegex(search as string, disallowedSymbolsRegex, RES_MSG.ERROR_MSG.DISALLOWED_SYMBOLS);

    // If validation fails, return the error response immediately
    if (validateSearchResult.error) {
      return ResponseHandler.error(res, {
        status: validateSearchResult.status,
        error: true,
        message: validateSearchResult.message,
      });
    }
    if (userId && !Types.ObjectId.isValid(userId as string)) {
      return ResponseHandler.error(res, {
        message: RES_MSG.COMMON.NO_FOUND,
        status: RESPONSES.BAD_REQUEST,
        error: true,
      });
    }

    const userIdObj = userId ? new Types.ObjectId(userId as string) : null;

    const filters = {
      ...(status && { status }),
      ...(startDate && endDate
        ? {
            createdAt: {
              $gte: new Date(startDate as string),
              $lte: new Date(endDate as string),
            },
          }
        : startDate
        ? { createdAt: { $gte: new Date(startDate as string) } }
        : endDate
        ? { createdAt: { $lte: new Date(endDate as string) } }
        : {}),
    };

    // const userDetails: PromiseResolve = await UserService.fetchUserDetails({ _id: userIdObj }, ["name", "email", "userImage"], ["_id"]);
    const projection = [
      'overview.title',
      'fee',
      'status',
      'overview.subTitle',
      'overview.icon',
      'overview.logo',
      'createdAt',
      'overview.cover',
      'projectDetails.assetName',
      'projectDetails.startDate',
      'projectDetails.endDate',
      'createdAt',
      'projectDetails.tokenTicker',
      'projectDetails.blockChainType',
      'projectDetails.assetType',
      'projectDetails.offeringName',
      'tokenAddress',
      'fundAddress',
      'erc20Address',
      'userId',
      'offeringFeeStatus',
      'iserc20',
    ];
    const offeringList: PromiseResolve = await OfferingService.getOfferingList(filters, projection, {
      page: Number(page),
      limit: Number(limit),
      ...(search && { search: search }),
      ...(sort && { sort: JSON.parse(sort as string) }),
    });

    return ResponseHandler.success(res, {
      status: offeringList.status || RESPONSES.SUCCESS,
      error: false,
      message: offeringList.message || RES_MSG.USER.USERS_FETCH,
      data: {
        ...offeringList.data,
      },
    });
  } catch (error) {
    logger.error(error, 'fetch Offering List Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise < PromiseResolve >}
 */
export async function getIssuerOfferingController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { page = 1, limit = 10, sort, search = '', issuerStatus = '' } = req.query;
    const disallowedSymbolsRegex = /[!+#$%^*(),?":{}|<>]/;
    const validateSearchResult = await CommonHelper.validateStringRegex(search as string, disallowedSymbolsRegex, RES_MSG.ERROR_MSG.DISALLOWED_SYMBOLS);

    // If validation fails, return the error response immediately
    if (validateSearchResult.error) {
      return ResponseHandler.error(res, {
        status: validateSearchResult.status,
        error: true,
        message: validateSearchResult.message,
      });
    }
    const filters = {
      isKyc: true,
      isIssuer: true,
      ...(issuerStatus && { issuerStatus }),
    };

    const projection = ['name', 'email', '_id', 'wallets', 'userImage', 'offeringStatusCounts', 'createdAt'];

    const userDetails = await UserService.fetchUserListWithOfferings(filters, projection, {
      page: Number(page),
      limit: Number(limit),
      ...(search && { search: search }),
      ...(sort && { sort: JSON.parse(sort as string) }),
    });
    return ResponseHandler.success(res, {
      status: userDetails.status,
      error: false,
      message: RES_MSG?.SUCCESS_MSG.ISSUER_LIST,
      data: userDetails.data,
    });
  } catch (error: any) {
    logger.error(error, 'getIssuerOfferingController');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}
/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise < PromiseResolve >}
 */
export async function requestedOfferings(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { page = 1, limit = 10, sort = '', search = '', status = '' } = req.query;
    const { userId, userType: userRole } = req.userInfo;
    const disallowedSymbolsRegex = /[!+#$%^*(),?":{}|<>]/;
    const validateSearchResult = await CommonHelper.validateStringRegex(search as string, disallowedSymbolsRegex, RES_MSG.ERROR_MSG.DISALLOWED_SYMBOLS);

    // If validation fails, return the error response immediately
    if (validateSearchResult.error) {
      return ResponseHandler.error(res, {
        status: validateSearchResult.status,
        error: true,
        message: validateSearchResult.message,
      });
    }
    // Build filters dynamically
    const filters: Record<string, any> = {
      ...(status && { status }),
    };
    if (userRole !== UserType.Admin) {
      filters.subAdminId = new Types.ObjectId(userId);
    }

    // Fetch requested offerings
    const userDetails = await OfferingService.requestedOfferings(filters, {
      page: Number(page),
      limit: Number(limit),
      ...(search && { search }),
      ...(sort && { sort }),
    });

    // Handle successful response
    return ResponseHandler.success(res, {
      status: userDetails.status || RESPONSES.SUCCESS,
      error: false,
      message: userDetails.message || RES_MSG.USER.USERS_FETCH,
      data: userDetails.data || [],
    });
  } catch (error) {
    // Log the error and send error response
    logger.error(error, 'Error in requestedOfferings');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}
export async function requestedOfferingsCsv(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { status = '' } = req.query; // Removed pagination and search
    const { userId, userType: userRole } = req.userInfo;

    // Build filters dynamically
    const filters: Record<string, any> = {
      ...(status && { status }),
    };
    if (userRole !== UserType.Admin) {
      filters.subAdminId = new Types.ObjectId(userId);
    }

    // Fetch requested offerings
    const userDetails = await OfferingService.requestedOfferingsCsv(filters);

    // Handle successful response
    return ResponseHandler.success(res, {
      status: userDetails.status || RESPONSES.SUCCESS,
      error: false,
      message: userDetails.message || RES_MSG.USER.USERS_FETCH,
      data: userDetails.data || [],
    });
  } catch (error) {
    // Log the error and send error response
    logger.error(error, 'Error in requestedOfferings');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise < PromiseResolve >}
 */
export async function assignOfferings(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { offeringId, subAdminId, userId } = req.body;
    const userIdObj = subAdminId ? new Types.ObjectId(subAdminId as string) : null;
    const { error, status } = await UserService.fetchUserDetails({ _id: userIdObj });
    if (error) {
      throw new CustomError('Invalid sub admin id', status);
    }
    const isApproved = await requestedOfferingSchema.findOne({ _id: new Types.ObjectId(offeringId), status: offeringStatusEnum.ASSIGNED });
    if (isApproved) {
      throw new CustomError('Already assigned sub admin', RESPONSES.CONFLICT);
    }

    // Permission Check for Sub Admin
    if (subAdminId) {
      const permission: any = await permissionSchema.findOne({ userId: userIdObj });

      const OFFERINGS_MODULE_ID = new Types.ObjectId('672c70ce05e65803246ffc44'); // Offerings
      const TRANSFER_AGENT_MODULE_ID = new Types.ObjectId('672c70e305e65803246ffc45'); // Transfer Agent

      const hasOfferingsPermission = permission.permission.find((p: any) => p.moduelsId.equals(OFFERINGS_MODULE_ID) && p.write === true);

      if (!hasOfferingsPermission) {
        throw new CustomError('This sub admin does not have write permission for Offerings Module', RESPONSES.CONFLICT);
      }

      const hasTransferAgentPermission = permission.permission.find((p: any) => p.moduelsId.equals(TRANSFER_AGENT_MODULE_ID) && p.write === true);

      if (!hasTransferAgentPermission) {
        throw new CustomError('This sub admin does not have write permission for Transfer Agent Module', RESPONSES.CONFLICT);
      }
    }

    // Assign the offering

    const offeringDetails: PromiseResolve = await OfferingService.requestOffering({ subAdminId: subAdminId.toString(), status: offeringStatusEnum.ASSIGNED }, { _id: offeringId });
    const _id = offeringDetails?.data?.userId;
    const { name } = await userSchema.findOne(_id);
    const data = await userSchema.findOne({ _id: subAdminId }, 'name email userImage');
    const details = {
      name,
      subadminName: data?.name,
      email: data?.email,
      date: new Date().toLocaleString('en-US', { timeZone: 'UTC' }), // Adds the date in a more readable format
    };
    emailHelper.sendEmailTemplate(details?.email, 'assignSubadmin', details);
    const result = await permissionSchema.findOneAndUpdate(
      { userId: subAdminId },
      {
        $set: {
          'permission.$[elem1].read': true,
          'permission.$[elem1].write': true,
          'permission.$[elem2].read': true,
        },
      },
      {
        arrayFilters: [{ 'elem1.moduelsId': new mongoose.Types.ObjectId('672c70ce05e65803246ffc44') }, { 'elem2.moduelsId': new mongoose.Types.ObjectId('672c70e305e65803246ffc45') }],
        new: true, // returns the updated document
      },
    );
    return ResponseHandler.success(res, {
      status: offeringDetails.status || RESPONSES.SUCCESS,
      error: false,
      message: offeringDetails.message || RES_MSG.USER.USERS_FETCH,
      data: offeringDetails.data || [],
    });
  } catch (error) {
    logger.error(error, 'assignedOfferings Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

// Class-based wrapper - same functions, just wrapped in class object
class OfferingController {
  public getOfferingDetailsController = getOfferingDetailsController;
  public rejectOffering = rejectOffering;
  public offeringReportController = offeringReportController;
  public createOfferingController = createOfferingController;
  public getOfferingListController = getOfferingListController;
  public getOfferingList = getOfferingList;
  public getIssuerOfferingController = getIssuerOfferingController;
  public requestedOfferings = requestedOfferings;
  public requestedOfferingsCsv = requestedOfferingsCsv;
  public assignOfferings = assignOfferings;
}

export default new OfferingController();
