import mongoose, { Schema, Document, Types } from 'mongoose';
import { offeringStatusEnum } from '../../../utils/common.interface';

export interface IWhitelist extends Document {
  userId: Types.ObjectId | string;
  offeringId: Types.ObjectId | string;
  address: string;
  template_id?: string;
  docuSignurl?: string;
  envelopeId?: string;
  isExpired: boolean;
  whiteListReason?: string;
  txHash?: string;
  isSigned: boolean;
  status?: offeringStatusEnum;
  taStatus?: offeringStatusEnum;
}

export interface IUpdateWhitelist {
  userId?: Types.ObjectId | string;
  offeringId?: Types.ObjectId | string;
  address?: string;
  status?: string;
  txHash?: string;
  whiteListReason?: string;
  taStatus?: offeringStatusEnum;
}

const whitelist: Schema<IWhitelist> = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: 'users', required: true },
    offeringId: { type: Schema.Types.ObjectId, ref: 'offerings', required: true },
    address: {
      type: String,
      required: true,
      set: (value: string) => (value ? value.toLowerCase() : value),
      // validate: [CommonHelper.validateEthereumAddress, 'Invalid Ethereum address'],
    },
    status: { type: String, enum: Object.values(offeringStatusEnum), default: offeringStatusEnum.STARTED },
    taStatus: { type: String, enum: Object.values(offeringStatusEnum), default: offeringStatusEnum.STARTED },
    template_id: { type: String, default: undefined },
    docuSignurl: { type: String, default: undefined },
    envelopeId: { type: String, default: undefined },
    txHash: { type: String, required: false, default: '---' },
    isExpired: { type: Boolean, default: false },
    isSigned: { type: Boolean, default: false },
    whiteListReason: { type: String, default: undefined },
  },
  { timestamps: true, versionKey: false },
);

const whitelistSchema = mongoose.model<IWhitelist>('Whitelist', whitelist);
export { whitelistSchema };
