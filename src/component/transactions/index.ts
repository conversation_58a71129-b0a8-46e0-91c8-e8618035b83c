import { Request, Response } from 'express';
import { PromiseResolve } from '../../utils/common.interface';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { ResponseHandler } from '../../helpers/response.helper';

import { Types } from 'mongoose';
import TransactionsService from './service';
import logger from '../../helpers/logging/logger.helper';
import CommonHelper from '../../helpers/common.helper';
// import {validateStringRegex}  '../../helpers/common.helper'

class TransactionController {
  /**
   * Handles fetching transactions with filters and pagination.
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise<void>}
   */
  public getTransactions = async (req: Request, res: Response): Promise<PromiseResolve | void> => {
    try {
      const { page = 1, limit = 10, sort = '', search = '', type = '' } = req.query;
      // Define the regex for disallowed symbols
      const disallowedSymbolsRegex = /[!+#$%^*(),?":{}|<>]/;

      // Validate the search parameter using the CommonHelper's validateStringRegex
      const validateSearchResult = await CommonHelper.validateStringRegex(search as string, disallowedSymbolsRegex, RES_MSG.ERROR_MSG.DISALLOWED_SYMBOLS);

      // If validation fails, return the error response immediately
      if (validateSearchResult.error) {
        return ResponseHandler.error(res, {
          status: validateSearchResult.status,
          error: true,
          message: validateSearchResult.message,
        });
      }

      const userId = new Types.ObjectId(req.userInfo.userId);
      const offeringId = new Types.ObjectId(req.params.offeringId);

      const filters = {
        ...{ status: type?.toString().toUpperCase() },
        userId,
        offeringId,
      };

      const result: PromiseResolve = await TransactionsService.fetchTranscations(filters, {
        page: Number(page),
        limit: Number(limit),
        ...(search && { search: search }),
        ...(sort && { sort: sort }),
      });

      return ResponseHandler.success(res, {
        status: result.status || RESPONSES.SUCCESS,
        error: false,
        message: result.message || RES_MSG.USER.USERS_FETCH,
        data: result.data || [],
      });
    } catch (error) {
      logger.error(error, 'getTransactions Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };
  public getTransactionsCsv = async (req: Request, res: Response): Promise<PromiseResolve | void> => {
    try {
      const { type = '' } = req.query; // Only keep the type parameter

      const userId = new Types.ObjectId(req.userInfo.userId);
      const offeringId = new Types.ObjectId(req.params.offeringId);

      // Filters object
      const filters = {
        ...{ status: type?.toString().toUpperCase() },
        userId,
        offeringId,
      };

      // Prepare pagination with default values for 'page' and 'limit'

      // Fetch transactions without pagination, search, and regex validation
      const result: PromiseResolve = await TransactionsService.fetchTransacationsCsv(filters);

      return ResponseHandler.success(res, {
        status: result.status || RESPONSES.SUCCESS,
        error: false,
        message: result.message || RES_MSG.USER.USERS_FETCH,
        data: result.data || [],
      });
    } catch (error) {
      logger.error(error, 'getTransactions Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };
}

export default new TransactionController();
