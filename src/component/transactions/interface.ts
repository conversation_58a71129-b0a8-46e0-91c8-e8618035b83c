import { FilterQuery } from 'mongoose';
import { IPagination, PromiseResolve } from '../../utils/common.interface';
import { EventLog } from './model/EventLogSchema.model';

export interface ITransactionsService {
  /**
   * @param {EventLog} searchDetails
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */

  fetchConvertList(searchDetails: FilterQuery<EventLog>, pagination?: IPagination): Promise<PromiseResolve>;

  /**
   * @param {EventLog} searchDetails
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */

  fetchTranscations(searchDetails: FilterQuery<EventLog>, pagination?: IPagination): Promise<PromiseResolve>;
  fetchTransacationsCsv(searchDetails: FilterQuery<EventLog>): Promise<PromiseResolve>;
}
