const mongoose = require('mongoose');

export interface EventLog {
  address?: string;
  topics?: string[];
  data?: string;
  blockNumber?: number;
  transactionHash?: string;
  transactionIndex?: number;
  blockHash?: string;
  logIndex?: number;
  removed?: boolean;
  event?: string;
  signature?: string;
  returnValues?: {
    wallet?: string;
    identity?: string;
    salt?: string;
    onchainID?: string;
    token?: string;
    fromAddress?: string;
    identityRegistry?: string;
    _token?: string;
    _ir?: string;
    _irs?: string;
    _tir?: string;
    _ctr?: string;
    _mc?: string;
    _salt?: string;
    _EquityConfigProxy?: string;
    mappingValue?: string;
    _investor?: string;
    orderID?: string;
    amountValue?: string;
    _asset?: string;
    id?: string;
    _erc3643?: string;
    _erc20?: string;
    userAddress?: string;
    OfferingAddress?: string;
    userID?: string;
    newFee?: string;
    timeStamp?: string;
    newAdminWallet?: string;
  };
  createdAt?: Date;
}

const EventLogSchema = new mongoose.Schema({
  address: { type: String, required: true },
  topics: [{ type: String }],
  data: { type: String },
  blockNumber: { type: Number },
  transactionHash: { type: String, unique: true, required: true },
  transactionIndex: { type: Number },
  blockHash: { type: String },
  logIndex: { type: Number },
  removed: { type: Boolean },
  event: { type: String },
  signature: { type: String },
  returnValues: {
    wallet: { type: String },
    identity: { type: String },
    salt: { type: String },
    onchainID: { type: String },
    token: { type: String },
    fromAddress: { type: String },
    identityRegistry: { type: String },
    _token: { type: String },
    _ir: { type: String },
    _irs: { type: String },
    _tir: { type: String },
    _ctr: { type: String },
    _mc: { type: String },
    _salt: { type: String },
    _EquityConfigProxy: { type: String },
    mappingValue: { type: String },
    _investor: { type: String },
    orderID: { type: String },
    amountValue: { type: String },
    _asset: { type: String },
    amount: { type: String },
    id: { type: String },
    _erc3643: { type: String },
    _erc20: { type: String },
    _amount: { type: String },
    userAddress: { type: String },
    OfferingAddress: { type: String },
    userID: { type: String },
    newFee: { type: String },
    timeStamp: { type: String },

    newAdminWallet: { type: String },

    // Add other fields from returnValues as necessary
  },
  createdAt: { type: Date, default: Date.now },
});

export default mongoose.model('EventLogSchema', EventLogSchema);
