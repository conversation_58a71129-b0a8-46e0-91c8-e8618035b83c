import { Request, Response } from 'express';
import { offeringStatusEnum, PromiseResolve, UserType } from '../../utils/common.interface';
import OfferingService from './service';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import CustomError from '../../helpers/customError.helper';
import { ResponseHandler } from '../../helpers/response.helper';
import logger from '../../helpers/logging/logger.helper';
import { Types } from 'mongoose';
import { offeringDocs } from '../../utils/constant';
import userClient from '../../_grpc/clients/user.client';
import { transferAgentSchema } from '../transferagent/transferagent.model';
import notificationClient from '../../_grpc/clients/notification.client';

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise < PromiseResolve >}
 */
export async function getOfferingNotification(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { userInfo } = req;
    const { userId } = userInfo;
    const payload = {
      page: req.query.page,
      limit: req.query.limit,
      userId: userId,
    };
    await notificationClient.client.getOfferingRequestNotification(payload, async (error: any, response: any) => {
      if (error || response?.error) {
        const errorMessage = error?.message || response?.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR;
        const errorStatus = error?.status || RESPONSES.INTERNAL_SERVER_ERROR;
        return ResponseHandler.error(res, {
          status: errorStatus,
          error: true,
          message: errorMessage,
        });
      }

      return ResponseHandler.success(res, {
        status: response.status || RESPONSES.SUCCESS,
        error: false,
        message: response.message || RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: {
          data: JSON.parse(response.data),
          totalCount: response.totalCount,
        },
      });
    });
  } catch (error: any) {
    logger.error(error, 'getOffering Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise < PromiseResolve >}
 */
export async function seenOfferingNotification(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { userInfo } = req;
    const { userId } = userInfo;
    const payload = {
      _id: req.query._id,
      userId: userId,
    };
    await notificationClient.client.seenOfferingRequestNotification(payload, async (error: any, response: any) => {
      if (error || response?.error) {
        const errorMessage = error?.message || response?.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR;
        const errorStatus = error?.status || RESPONSES.INTERNAL_SERVER_ERROR;

        console.error('gRPC Error:', error || response);
        return ResponseHandler.error(res, {
          status: errorStatus,
          error: true,
          message: errorMessage,
        });
      }

      console.log('responseeeeeeeeeee', JSON.parse(response.data));

      return ResponseHandler.success(res, {
        status: response.status || RESPONSES.SUCCESS,
        error: false,
        message: response.message || RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: JSON.parse(response.data),
      });
    });
  } catch (error: any) {
    logger.error(error, 'getOffering Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}
