import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { PromiseResolve } from '../../utils/common.interface';
import { FilterQuery, Types } from 'mongoose';
import logger from '../../helpers/logging/logger.helper';

const OfferingService = {
  /**
   * @param {IOffering} searchDetails
   * @param {IOffering} fields
   * @param {IOffering} excludeFields
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  // async fetchOfferingDetails(searchDetails: FilterQuery<IOffering>, fields?: string[], excludeFields?: string[]): Promise<PromiseResolve> {
  //   try {
  //     const query = offeringSchema.findOne(searchDetails);
  //     if (fields && fields.length > 0) {
  //       const fieldsString = fields.join(' ');
  //       query.select(fieldsString);
  //     }
  //     if (excludeFields && excludeFields.length > 0) {
  //       const excludeFieldsString = excludeFields.map((field) => `-${field}`).join(' ');
  //       query.select(excludeFieldsString);
  //     }
  //     const offeringDetails: IOffering | null = await query.exec();
  //     if (offeringDetails) {
  //       return {
  //         status: RESPONSES.SUCCESS,
  //         error: false,
  //         message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
  //         data: offeringDetails.toObject(),
  //       };
  //     } else {
  //       return {
  //         status: RESPONSES.NOTFOUND,
  //         error: true,
  //         message: RES_MSG.COMMON.NO_OFFERING,
  //       };
  //     }
  //   } catch (error) {
  //     logger.error(error, 'fetchOfferingDetails error');
  //     return {
  //       status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
  //       error: true,
  //       message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
  //     };
  //   }
  // },
  // /**
  //  * @param {IRequestedOffering} searchDetails
  //  * @returns {Promise<PromiseResolve>}
  //  * @memberof OfferingService
  //  */
  // async fetchAssignedOfferingDetails(searchDetails: FilterQuery<IRequestedOffering>): Promise<PromiseResolve> {
  //   try {
  //     const offeringDetails = await requestedOfferingSchema.findOne(searchDetails);
  //     if (offeringDetails) {
  //       return {
  //         status: RESPONSES.SUCCESS,
  //         error: false,
  //         message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
  //         data: offeringDetails.toObject(),
  //       };
  //     } else {
  //       return {
  //         status: RESPONSES.NOTFOUND,
  //         error: true,
  //         message: RES_MSG.COMMON.NO_OFFERING,
  //       };
  //     }
  //   } catch (error) {
  //     logger.error(error, 'fetchAssignedOfferingDetails error');
  //     return {
  //       status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
  //       error: true,
  //       message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
  //     };
  //   }
  // },
};

export default OfferingService;
