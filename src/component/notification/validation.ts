import { JoiValidationResult } from '../../utils/common.interface';
import * as Jo<PERSON> from 'joi';
import * as joiOptions from '../../helpers/joiError.filter.helper';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import logger from '../../helpers/logging/logger.helper';

class NotificationValidation {
  async getOfferingNotification(query: { page?: number; limit?: number }): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        page: Joi.number().integer().min(1).default(1).optional(),
        limit: Joi.number().integer().min(1).default(10).optional(),
        // status: Joi.string().default('').optional(),
        // searchQuery: Joi.string().default('').optional(),
      });

      const { error, value } = schema.validate(query, joiOptions.options);
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }

      return {
        error: false,
        value,
      };
    } catch (error) {
      logger.error(error, 'getOrdersfromOfferingIdValidation Error');
      return {
        error: true,
        value: '',
        message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: RESPONSES.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async notificationSeenJoi(query: { _id?: string[] }): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        _id: Joi.array()
          .items(
            Joi.string().length(24).hex().messages({
              'string.base': 'Each _id must be a string.',
              'string.length': 'Each _id must be exactly 24 characters long.',
              'string.hex': 'Each _id must contain only hexadecimal characters (0-9, a-f).',
            }),
          )
          .required()
          .messages({
            'array.base': '_id must be an array of strings.',
            'any.required': '_id is required.',
          }),
      });

      const { error, value } = schema.validate(query, joiOptions.options);
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }

      return {
        error: false,
        value,
      };
    } catch (error) {
      logger.error(error, 'getOfferingNotification Validation Error');
      return {
        error: true,
        value: '',
        message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: RESPONSES.INTERNAL_SERVER_ERROR,
      };
    }
  }
}

export default new NotificationValidation();
