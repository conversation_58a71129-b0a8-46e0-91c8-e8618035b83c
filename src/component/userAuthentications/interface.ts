import { FilterQuery } from 'mongoose';
import { IPagination, PromiseResolve } from '../../utils/common.interface';
import { IUserModel, IUpdateUserModel } from './user.model';

export interface IUserService {
  /**
   * @param {IUserModel} body
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  createUser(body: any): Promise<PromiseResolve>;

  /**
   * @param {IUserModel} searchDetails
   * @param {IUserModel} fields
   * @param {IUserModel} excludeFields
   * @param {Boolean} isKyc
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  fetchUserDetails(searchDetails: FilterQuery<IUserModel>, fields?: string[], excludeFields?: string[], isKyc?: Boolean): Promise<PromiseResolve>;

  /**
   * @param {IUpdateUserModel} searchDetails
   * @param {Array} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */

  fetchUserList(searchDetails: FilterQuery<IUpdateUserModel>, projection?: string[], pagination?: IPagination): Promise<PromiseResolve>;

  fetchUserListcsv(searchDetails: FilterQuery<IUpdateUserModel>, projection?: string[]): Promise<PromiseResolve>;

  /**
   * @param {IUpdateUserModel} searchDetails
   * @param {Array} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */

  fetchUserListWithOfferings(searchDetails: FilterQuery<IUpdateUserModel>, projection?: string[], pagination?: IPagination): Promise<PromiseResolve>;

  fetchUserListWithOfferingscsv(searchDetails: FilterQuery<IUpdateUserModel>, projection?: string[], pagination?: IPagination): Promise<PromiseResolve>;

  /**
   * @param {IUpdateUserModel} body
   * @param {IUserModel} filter
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  updateUserDetails(body: IUpdateUserModel, filter: FilterQuery<IUserModel>): Promise<PromiseResolve>;

  /**
   * @param {string} _id
   * @param {string} password
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  addPassword(_id: string, password: string): Promise<PromiseResolve>;

  /**
   * @param {PasswordModel} _id
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  fetchRecentPasswords(_id: string): Promise<PromiseResolve>;

  singleOfferingReport(searchDetails: any, pagination: IPagination): Promise<PromiseResolve>;

  searchUsersAndAgents(search: string): Promise<PromiseResolve>;

  // /**
  //  * @param {IUserDetails} body
  //  * @param {IUserDetails} filter
  //  * @returns {Promise<PromiseResolve>}
  //  * @memberof UserService
  //  */
  // findOneAndUpdate(
  //   body: IUpdateUserModel,
  //   filter: FilterQuery<IUserModel>
  // ): Promise<PromiseResolve>;
}
