import { IUserService } from './interface';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { PromiseResolve, IPagination, UserType, soketTypeEnum } from '../../utils/common.interface';
import { IUserModel, IUpdateUserModel, userSchema, passwordSchema, PasswordModel } from './user.model';
import { FilterQuery, Types } from 'mongoose';
import { maxPasswordHistory } from '../../utils/constant';
import logger from '../../helpers/logging/logger.helper';
import * as bcrypt from 'bcrypt';
import { OrderSchema } from '../offerings/models/order.model';
import { transferAgentSchema } from '../transferagent/transferagent.model';
import { socketHelper } from '../../helpers/socket.helper';

// import CommonHelper from '../../utils/common.helper';

const UserService: IUserService = {
  /**
   * @param {IUserModel} searchDetails
   * @param {IUserModel} fields
   * @param {IUserModel} excludeFields
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */

  async fetchUserDetails(searchDetails: FilterQuery<IUserModel>, fields?: string[], excludeFields?: string[]): Promise<PromiseResolve> {
    try {
      const userDetailsQuery = userSchema.findOne(searchDetails);
      if (fields && fields.length > 0) {
        const fieldsString = fields.join(' ');
        userDetailsQuery.select(fieldsString);
      }

      if (excludeFields && excludeFields.length > 0) {
        const excludeFieldsString = excludeFields.map((field) => `-${field}`).join(' ');
        userDetailsQuery.select(excludeFieldsString);
      }

      const userDetails: IUserModel = await userDetailsQuery.exec();

      if (userDetails && userDetails.email) {
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
          data: {
            ...userDetails.toObject(),
          },
        };
      }
      return {
        status: RESPONSES.NOTFOUND,
        error: true,
        message: RES_MSG.COMMON.NO_USER,
      };
    } catch (error) {
      logger.error(error, 'fetchUserDetails error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  /**
   * @param {IUpdateUserModel} data
   * @param {IUserModel} filter
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  async updateUserDetails(data: IUpdateUserModel, filter: FilterQuery<IUserModel>): Promise<PromiseResolve> {
    try {
      if (data?.password) {
        const salt = await bcrypt.genSalt(10);
        data.password = await bcrypt.hash(data.password, salt);
      }
      const updateUserResp = await userSchema.findOneAndUpdate(filter, data, {
        new: true,
        runValidators: true,
        upsert: true, // Insert if the document doesn't exist
      });
      const detail = {
        userId: updateUserResp?._id,
        status: data?.kycStatus,
        onchainId: data?.onchainID,
      };
      await socketHelper.sendMessageToUserWithoutAuth('temp_id', soketTypeEnum.UPDATEKYCSTATUS, detail);

      if (updateUserResp) {
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.USER.USER_UPDATION_SUCCESS,
          data: updateUserResp,
        };
      }

      return {
        status: RESPONSES.BAD_REQUEST,
        error: true,
        message: RES_MSG.ERROR_MSG.USER_UPDATION_ERROR,
      };
    } catch (error) {
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  /**
   * @param {IUserModel} body
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  async createUser(body: IUserModel): Promise<PromiseResolve> {
    try {
      const createQuery: IUserModel = await userSchema.create(body);
      if (createQuery) {
        return {
          status: RESPONSES.CREATED,
          error: false,
          message: RES_MSG.SUCCESS_MSG.REGISTER_SUCCESS,
          data: createQuery,
        };
      }
      return {
        status: RESPONSES.BAD_REQUEST,
        error: true,
        message: RES_MSG.COMMON.SOMETHING_WRONG,
      };
    } catch (error) {
      logger.error(error, 'createUser error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  /**
   * @param {IUpdateUserModel} filters
   * @param {any[]} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */

  async fetchUserList(filters: IUpdateUserModel, projection: any[], pagination: IPagination): Promise<PromiseResolve> {
    try {
      console.log('in featchuser list graph-------');

      const { page = 1, limit = 10, sort = { createdAt: -1 }, search } = pagination;
      const skip = (page - 1) * limit;
      const query: FilterQuery<any> = {};
      const addFilter = (key: keyof IUpdateUserModel, value: any) => {
        if (typeof value !== 'undefined') query[key] = value;
      };

      const regexPattern = new RegExp(`${search}$`, 'i'); // Matches the end

      if (search) {
        // Escape any special characters in the search term to avoid issues with regex
        const safeSearch = search.replace(/[.*+?^=!:${}()|\[\]\/\\]/g, '\\$&');

        // Create regex for search term, this will search the string anywhere (no need for $ at the end)
        const regexPattern = new RegExp(safeSearch, 'i');

        // Add the regex conditions to query for email, name, or mobile
        query.$or = [{ email: { $regex: regexPattern } }, { name: { $regex: regexPattern } }, { mobile: { $regex: regexPattern } }];
      }

      query.email = {
        $ne: process.env.EMAIL,
      };

      query.userType = {
        $nin: [UserType.Admin, UserType.Subadmin], // userType must not be Admin or Subadmin
      };

      addFilter('isActive', filters.isActive);
      addFilter('isEmailVerify', filters.isEmailVerify);
      addFilter('isMobileVerify', filters.isMobileVerify);
      addFilter('isSocialMedia', filters.isSocialMedia);
      addFilter('isDeleted', filters.isDeleted);
      addFilter('kycStatus', filters.kycStatus);
      addFilter('userType', filters.userType);
      addFilter('countryCode', filters.countryCode);
      addFilter('isKyc', filters.isKyc);
      addFilter('isIssuer', filters.isIssuer);
      addFilter('issuerStatus', filters.issuerStatus);

      const totalCount = await userSchema.countDocuments(query).exec();

      const users = await userSchema.find(query).select(projection).sort(sort).skip(skip).limit(limit).exec();

      const totalPages = Math.ceil(totalCount / limit);

      return {
        data: {
          user: users,
          currentPage: page,
          totalPages,
          totalCount,
          nextPage: page < totalPages ? page + 1 : null,
          previousPage: page > 1 ? page - 1 : null,
        },
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      throw new Error(`Error fetching users: ${error.message}`);
    }
  },

  async fetchUserListcsv(filters: IUpdateUserModel, projection: any[]): Promise<PromiseResolve> {
    try {
      const query: FilterQuery<any> = {};
      const addFilter = (key: keyof IUpdateUserModel, value: any) => {
        if (typeof value !== 'undefined') query[key] = value;
      };

      query.email = {
        $ne: process.env.EMAIL,
      };
      query.userType = {
        $nin: [UserType.Admin, UserType.Subadmin], // userType must not be Admin or Subadmin
      };
      addFilter('createdAt', filters.createdAt);
      addFilter('isKyc', filters.isKyc);
      addFilter('isIssuer', filters.isIssuer);
      addFilter('issuerStatus', filters.issuerStatus);

      const totalCount = await userSchema.countDocuments(query).exec();

      const users = await userSchema.find(query).select(projection).sort({ createdAt: -1 });

      return {
        data: {
          user: users,
          totalCount,
        },

        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      throw new Error(`Error fetching users: ${error.message}`);
    }
  },

  /**
   * @param {IUpdateUserModel} filters
   * @param {any[]} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  async fetchUserListWithOfferings(filters: IUpdateUserModel, projection: any[], pagination: IPagination): Promise<PromiseResolve> {
    try {
      const { page, limit, sort, search } = pagination;
      const skip = (page - 1) * limit;
      const searchQuery: FilterQuery<any> = {};

      const addFilter = (key: keyof IUpdateUserModel, value: any) => {
        if (typeof value !== 'undefined') searchQuery[key] = value;
      };

      if (search) {
        searchQuery.$or = [{ name: { $regex: search, $options: 'i' } }, { email: { $regex: search, $options: 'i' } }];
      }

      const projectionObject = projection.reduce((acc: any, field: string) => ({ ...acc, [field]: 1 }), {});

      searchQuery.userType = { $ne: UserType.Admin };
      addFilter('isKyc', filters.isKyc);
      addFilter('isIssuer', filters.isIssuer);
      addFilter('isActive', true);
      addFilter('issuerStatus', filters.issuerStatus);

      const pipeline = [
        {
          $match: searchQuery,
        },
        {
          $lookup: {
            from: 'offerings',
            localField: '_id',
            foreignField: 'userId',
            as: 'offerings',
            pipeline: [
              {
                $match: {
                  isDelete: false,
                },
              },
            ],
          },
        },
        {
          $match: {
            offerings: { $ne: [] },
          },
        },
        {
          $addFields: {
            offeringStatusCounts: {
              PENDING: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'PENDING'] },
                  },
                },
              },
              REJECTED: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'REJECTED'] },
                  },
                },
              },
              APPROVED: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'APPROVED'] },
                  },
                },
              },
              RESUBMIT: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'RESUBMIT'] },
                  },
                },
              },
            },
          },
        },
        // New stage to remove users where all counts are 0
        {
          $match: {
            $or: [
              { 'offeringStatusCounts.PENDING': { $gt: 0 } },
              { 'offeringStatusCounts.REJECTED': { $gt: 0 } },
              { 'offeringStatusCounts.APPROVED': { $gt: 0 } },
              { 'offeringStatusCounts.RESUBMIT': { $gt: 0 } },
            ],
          },
        },
      ];
      console.log(JSON.stringify(pipeline), '-------------');
      // Clone the pipeline to count total users after filtering
      const countPipeline = [...pipeline, { $count: 'totalCount' }];

      // Execute both queries
      const [userWithOfferings, totalCountResult] = await Promise.all([
        userSchema.aggregate([...pipeline, { $project: { ...projectionObject } }, { $sort: sort || { createdAt: -1 } }, { $skip: skip }, { $limit: limit }]),
        userSchema.aggregate(countPipeline),
      ]);

      const totalCount = totalCountResult.length > 0 ? totalCountResult[0].totalCount : 0;
      const totalPages = Math.ceil(totalCount / limit);

      return {
        data: {
          users: userWithOfferings,
          currentPage: page,
          totalPages,
          totalCount,
          nextPage: page < totalPages ? page + 1 : null,
          previousPage: page > 1 ? page - 1 : null,
        },
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      logger.error(error, 'fetchUserListWithOfferings error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  async fetchUserListWithOfferingscsv(filters: IUpdateUserModel, projection: any[]): Promise<PromiseResolve> {
    try {
      const searchQuery: FilterQuery<any> = {};
      const addFilter = (key: keyof IUpdateUserModel, value: any) => {
        if (typeof value !== 'undefined') searchQuery[key] = value;
      };
      const projectionObject = projection.reduce((acc: any, field: string) => ({ ...acc, [field]: 1 }), {});

      searchQuery.userType = { $ne: UserType.Admin };
      addFilter('isKyc', filters.isKyc);
      addFilter('isIssuer', filters.isIssuer);
      addFilter('isActive', true);
      addFilter('createdAt', filters.createdAt);
      addFilter('isEmailVerify', true);

      const userWithOfferings = await userSchema.aggregate([
        { $match: searchQuery },
        { $sort: { createdAt: -1 } },
        {
          $lookup: {
            from: 'offerings',
            localField: '_id',
            foreignField: 'userId',
            as: 'offerings',
          },
        },
        {
          $addFields: {
            offeringStatusCounts: {
              PENDING: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'PENDING'] },
                  },
                },
              },
              REJECTED: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'REJECTED'] },
                  },
                },
              },
              APPROVED: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'APPROVED'] },
                  },
                },
              },
              RESUBMIT: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'RESUBMIT'] },
                  },
                },
              },
            },
          },
        },
        {
          $sort: {
            // 'offeringStatusCounts.APPROVED': -1, // Highest approved first
            createdAt: -1, // Newest first if approved counts are equal
          },
        },
        {
          $project: {
            ...projectionObject,
          },
        },
      ]);
      const totalCount = await userSchema.countDocuments(searchQuery).exec();

      return {
        data: {
          users: userWithOfferings,
          totalCount,
        },
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      logger.error(error, 'fetchUserListWithOfferings error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  /**
   * @param {string} _id
   * @param {string} password
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  async addPassword(_id: string, password: string): Promise<PromiseResolve> {
    try {
      const userPasswords = await passwordSchema.findOne({ _id });

      // If the user has a password history
      if (userPasswords) {
        // Check if the password is already used
        if (userPasswords.passwords.includes(password)) {
          return {
            status: RESPONSES.BAD_REQUEST,
            error: true,
            message: RES_MSG.ERROR_MSG.PASSWORD_RECENTLY_USED,
          };
        }

        // Add the new password and manage history length
        userPasswords.passwords.push(password);
        if (userPasswords.passwords.length > maxPasswordHistory) {
          userPasswords.passwords.shift(); // Remove oldest password
        }

        const updatedQuery = await userPasswords.save();
        return {
          status: RESPONSES.CREATED,
          error: false,
          message: RES_MSG.SUCCESS_MSG.REGISTER_SUCCESS,
          data: updatedQuery,
        };
      }

      // If no password history exists, create a new entry
      const createQuery = await passwordSchema.create({
        _id,
        passwords: [password],
      });

      return {
        status: RESPONSES.CREATED,
        error: false,
        message: RES_MSG.SUCCESS_MSG.REGISTER_SUCCESS,
        data: createQuery,
      };
    } catch (error: any) {
      logger.error(error, 'addPassword error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },
  /**
   * @param {PasswordModel} searchDetails
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  async fetchRecentPasswords(_id: string): Promise<PromiseResolve> {
    try {
      const recentPasswords: PasswordModel[] = await passwordSchema.findOne({
        _id,
      });

      if (recentPasswords) {
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
          data: recentPasswords,
        };
      }
      return {
        status: RESPONSES.NOTFOUND,
        error: true,
        message: RES_MSG.COMMON.NO_USER,
      };
    } catch (error) {
      logger.error(error, 'fetchRecentPasswords error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  async singleOfferingReport(searchDetails: any, pagination: IPagination): Promise<PromiseResolve> {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 }, search } = pagination;
      const skip = (page - 1) * limit;
      // Build the search query
      let offeringId = searchDetails?.offeringId;

      const searchQuery: FilterQuery<any> = {};

      if (search) {
        searchQuery.$or = [{ 'userData.email': { $regex: search, $options: 'i' } }, { 'userData.name': { $regex: search, $options: 'i' } }];
      }
      const pipeline: any[] = [
        {
          $match: {
            status: 'MINTED',
            offeringId: new Types.ObjectId(offeringId),
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'userData',
          },
        },
        {
          $lookup: {
            from: 'offerings',
            localField: 'offeringId',
            foreignField: '_id',
            as: 'offeringData',
          },
        },
        {
          $unwind: {
            path: '$offeringData',
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $unwind: {
            path: '$userData',
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $facet: {
            offering: [
              {
                $match: {
                  ...(search
                    ? {
                        $or: [
                          {
                            'userData.email': { $regex: search, $options: 'i' },
                          },
                          {
                            'userData.name': { $regex: search, $options: 'i' },
                          },
                        ],
                      }
                    : {}),
                },
              },
              {
                $project: {
                  _id: 0,
                  userId: '$userData._id',
                  email: '$userData.email',
                  name: '$userData.name',
                  walletaddress: {
                    type: {
                      $arrayElemAt: ['$userData.wallets.type', 0],
                    },
                    address: {
                      $arrayElemAt: ['$userData.wallets.address', 0],
                    },
                    isVerify: {
                      $arrayElemAt: ['$userData.wallets.isVerify', 0],
                    },
                  },
                  offeringName: '$offeringData.projectDetails.offeringName',
                  erc20Address: '$offeringData.erc20Address',
                  fundAddress: '$offeringData.fundAddress',
                  tokenAddress: '$offeringData.tokenAddress',
                  offeringId: '$offeringData._id',
                },
              },
              {
                $group: {
                  _id: {
                    userId: '$userId',
                    offeringId: '$offeringId',
                  },
                  userId: { $first: '$userId' },
                  email: { $first: '$email' },
                  name: { $first: '$name' },
                  walletaddress: {
                    $first: '$walletaddress',
                  },
                  offeringName: {
                    $first: '$offeringName',
                  },
                  offeringId: { $first: '$offeringId' },
                },
              },
              { $skip: skip },
              { $limit: limit },
            ],
            totalInvestorsData: [
              {
                $group: {
                  _id: '$userData.email',
                },
              },
              {
                $count: 'totalInvestors',
              },
            ],
          },
        },
        {
          $addFields: {
            totalCount: { $size: '$offering' },
            totalInvestors: {
              $arrayElemAt: ['$totalInvestorsData.totalInvestors', 0],
            },
          },
        },
      ];

      const order = await OrderSchema.aggregate(pipeline);

      const totalCount = order[0].totalCount || 0;
      const totalPages = Math.ceil(totalCount / limit);

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.COMMON.RECORD_FETCH,
        data: {
          offering: order[0].offering, // The fetched data
          totalInvestors: order[0]?.totalInvestors || 0,
          currentPage: page,
          totalPages: totalPages,
          totalCount: totalCount,
          nextPage: page < totalPages ? page + 1 : null,
          previousPage: page > 1 ? page - 1 : null,
        },
      };
    } catch (error) {
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  async searchUsersAndAgents(search: string): Promise<PromiseResolve> {
    try {
      // Case-insensitive regex search
      const searchRegex = new RegExp(search, 'i');

      // Query for matching users
      const userQuery = {
        $or: [{ email: searchRegex }, { walletAddress: searchRegex }],
      };

      // Query for matching transfer agents
      const transferAgentQuery = {
        $or: [{ email: searchRegex }, { walletAddress: searchRegex }],
      };

      // Execute both queries in parallel
      const [users, transferAgents] = await Promise.all([
        userSchema.find(userQuery, { _id: 1, email: 1, walletAddress: 1, name: 1 }),
        transferAgentSchema.find(transferAgentQuery, { _id: 1, email: 1, walletAddress: 1, name: 1 }),
      ]);

      // Merge the results
      const result = [...users, ...transferAgents];

      return {
        status: RESPONSES.SUCCESS,
        error: result.length > 0 ? true : false,
        message: 'Search results retrieved successfully',
        data: result,
      };
    } catch (error) {
      logger.error('Error searching users and transfer agents:', error);
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: 'Internal server error',
        data: null,
      };
    }
  },
};

export default UserService;
