import { Request, Response } from 'express';
import { DocumentFolderTypesEnum, IssuerStatus, KycStatus, orderStatusEnum, PromiseResolve, UserType } from '../../utils/common.interface';
import UserService from './service';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { IUpdateUserModel, IUserModel, userSchema } from './user.model';
import CustomError from '../../helpers/customError.helper';
import RedisHelper from '../../helpers/redis.helper';
import { ResponseHandler } from '../../helpers/response.helper';
import CommonHelper from '../../helpers/common.helper';
import * as bcrypt from 'bcrypt';
const geoip = require('geoip-lite');
import { authenticator } from 'otplib';
import { toDataURL } from 'qrcode';
import CONFIG from '../../config/env';
import logger from '../../helpers/logging/logger.helper';
import { otpType } from '../../utils/common.interface';
import userClient from '../../_grpc/clients/user.client';
import mongoose from 'mongoose';
import emailHelper from '../../helpers/email.helper';
import OfferingService from '../offerings/service';
import { feeSchema } from './fee.model';
import { OrderSchema } from '../offerings/models/order.model';
import { Types } from 'mongoose';
import CloudHelper from '../../helpers/cloud.helper';

const kycCount = process.env.KYCCOUNT;
const walletAddress = process.env.ADMINADDRESS;
/**
 * @exports
 * @param {IUserModel} req
 * @param {IUserModel} res
 * @returns {Promise<PromiseResolve>}
 */
export async function loginController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { email, password } = req.body;
    const userDetails: PromiseResolve = await UserService.fetchUserDetails({ email }, [], ['isIssuer', 'issuerStatus', 'createdAt', 'updatedAt']);

    const user: IUserModel = userDetails?.data;

    if (userDetails?.error || !user?.password || (user?.userType !== UserType.Admin && user?.userType !== UserType.Subadmin)) {
      throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.BAD_REQUEST);
    }
    let role;
    if (user.userType == UserType.Admin) {
      role = '1';
    } else {
      role = '2';
    }
    const verifyPassResp: PromiseResolve = await CommonHelper.verifyPassword(password, user.password);

    if (verifyPassResp.error) {
      throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, verifyPassResp.status);
    }
    if (!user.isActive) {
      throw new CustomError(RES_MSG.COMMON.ADMIN_BLOCK_USER, RESPONSES.FORBIDDEN);
    } else if (!user.isEmailVerify) {
      const otpResponse = await CommonHelper.sendOTP(user._id, otpType.LOGIN, user.email);
      if (otpResponse.error) throw new CustomError(otpResponse.message, otpResponse.status);
      return ResponseHandler.success(res, {
        status: userDetails.status || RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USER_NOT_VERIFIED,
        data: {
          isOtpActive: user.isOtpActive,
        },
      });
    } else if (user.isOtpActive) {
      const otpResponse = await CommonHelper.sendOTP(user._id, otpType.LOGIN, user.email);
      if (otpResponse.error) throw new CustomError(otpResponse.message, otpResponse.status);
      return ResponseHandler.success(res, {
        status: userDetails.status || RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.OTP_SENT_SUCCESS,
        data: {
          isOtpActive: user.isOtpActive,
          role,
        },
      });
    } else if (user.is2FAActive) {
      //token for twofa verification
      const tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email }, false, 'twoFA', CONFIG.JWT_AUTH.JWT_2FA_EXPIRE);
      if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);
      return ResponseHandler.success(res, {
        status: userDetails.status || RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.TWO_FA.PENDING,
        data: {
          is2FAActive: user.is2FAActive,
          role,
          ...tokenResp.data,
        },
      });
    }
    await userSchema.updateOne({ _id: user._id }, { $set: { lastLogin: new Date() } });
    const tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email, userType: user.userType }, true);
    if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);

    const emailDetail = {
      name: 'Admin',
    };

    emailHelper.sendEmailTemplate(email, 'login', emailDetail);
    return ResponseHandler.success(res, {
      status: userDetails.status || RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.USER.LOGIN_SUCCESS,
      data: {
        ...tokenResp.data,
        walletAddress: user.walletAddress || walletAddress,
        //  isOtpActive: user.isOtpActive,
        role,
        is2FAActive: user.is2FAActive,
        isWalletPublished: user.userType ? true : user.onchainID ? true : false,
      },
    });
  } catch (error: any) {
    logger.error(error, 'loginController Error');

    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {IUserModel} req
 * @returns {Promise<PromiseResolve>}
 */
export async function verifyController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { email, otp, type } = req.body;

    const userDetails = await UserService.fetchUserDetails({
      email,
      isActive: true,
    });

    if (userDetails.error) {
      throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
    }

    const user = userDetails.data;
    const storedOTP = await CommonHelper.getOTP(user._id, type);

    if (storedOTP.error || !storedOTP.data.otp || storedOTP.data.otp != otp) {
      throw new CustomError(RES_MSG.ERROR_MSG.INVALID_OTP, RESPONSES.BAD_REQUEST);
    }

    let tokenResp;
    switch (type) {
      // Verify during login
      case otpType.LOGIN:
        if (!user.isEmailVerify) {
          const otpResponse = await CommonHelper.sendOTP(user._id, otpType.LOGIN, user.email);
          if (otpResponse.error) throw new CustomError(otpResponse.message, otpResponse.status);

          return ResponseHandler.success(res, {
            status: userDetails.status || RESPONSES.SUCCESS,
            error: false,
            message: RES_MSG.USER.USER_NOT_VERIFIED,
            data: { isOtpActive: user.isOtpActive },
          });
        }

        if (user.is2FAActive) {
          tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email }, false, 'twoFA', CONFIG.JWT_AUTH.JWT_2FA_EXPIRE);
          if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);

          return ResponseHandler.success(res, {
            status: userDetails.status || RESPONSES.SUCCESS,
            error: false,
            message: RES_MSG.TWO_FA.PENDING,
            data: {
              is2FAActive: user.is2FAActive,
              ...tokenResp.data,
            },
          });
        }

        const ip = req.headers['x-forwarded-for'] || req.ip;
        const geo = geoip.lookup(ip);
        const time = new Date().toLocaleString();

        const emailDetail = { name: process.env.NAME, ip, geo, time };
        tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email, userType: user.userType }, true);
        if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);

        emailHelper.sendEmailTemplate(email, 'login', emailDetail);
        return ResponseHandler.success(res, {
          status: userDetails.status || RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.USER.LOGIN_SUCCESS,
          data: {
            ...tokenResp.data,
            is2FAActive: user.is2FAActive,
          },
        });

      case otpType.FORGOT:
        tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email }, false, 'forgetToken', CONFIG.JWT_AUTH.FORGOT_EXPIRE_TIME);
        if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);

        return ResponseHandler.success(res, {
          status: userDetails.status || RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.USER.OTP_SUCCESS,
          data: {
            userType: UserType.Admin,
            //  isOtpActive: user.isOtpActive,
            walletAddress: walletAddress,
            accessToken: tokenResp.data.accessToken,
          },
        });

      default:
        throw new CustomError(RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, RESPONSES.INTERNAL_SERVER_ERROR);
    }
  } catch (error) {
    logger.error(error, 'verifyController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {IUserModel} req
 * @returns {Promise<PromiseResolve>}
 */
export async function reSendOtpController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { email, type } = req.body;
    const userLockResp: PromiseResolve = await CommonHelper.userLock(email);
    if (userLockResp.error) throw new CustomError(userLockResp.message, userLockResp.status);

    const userDetails: PromiseResolve = await UserService.fetchUserDetails({
      email,
    });
    if (type == 'forgot') {
      if (userDetails.error)
        return ResponseHandler.success(res, {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.ERROR_MSG.FORGOT_CRED,
        });
    }

    if (userDetails.error) throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);

    const otpResponse = await CommonHelper.sendOTP(userDetails.data._id, type, userDetails.data.email);
    if (otpResponse.error) throw new CustomError(otpResponse.message, otpResponse.status);
    return ResponseHandler.success(res, {
      status: userDetails.status || RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.ERROR_MSG.FORGOT_CRED,
      data: null,
    });
  } catch (error) {
    logger.error(error, 'loginController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise < PromiseResolve >}
 */
export async function getUsersListController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const {
      page = 1,
      limit = 10,
      sort = JSON.stringify({ createdAt: -1 }),
      search = '',

      isActive,
      isDeleted,
      kycStatus = '',
      userType = '',
      countryCode = '',
      isKyc,
    } = req.query;
    const disallowedSymbolsRegex = /[!+#$%^*(),?":{}|<>]/;
    const validateSearchResult = await CommonHelper.validateStringRegex(search as string, disallowedSymbolsRegex, RES_MSG.ERROR_MSG.DISALLOWED_SYMBOLS);

    // If validation fails, return the error response immediately
    if (validateSearchResult.error) {
      return ResponseHandler.error(res, {
        status: validateSearchResult.status,
        error: true,
        message: validateSearchResult.message,
      });
    }
    const filters = {
      ...(isActive !== undefined && { isActive: isActive === 'true' }),
      ...(isDeleted !== undefined && { isDeleted: isDeleted === 'true' }),
      ...(kycStatus !== '' && kycStatus && { kycStatus }), // Only include kycStatus if it's not an empty string
      ...(userType && { userType }),
      ...(countryCode && { countryCode }),
      ...(isKyc !== undefined && { isKyc: isKyc === 'true' }),
    };

    let sortCriteria = JSON.parse(sort as string);
    const projection = [
      'name',
      'email',
      'userImage',
      'kycStatus',
      'mobile',
      'isKyc',
      'mainInformation.nationality',
      'institutions.companyInformation',
      'isActive',
      'userType',
      'countryCode',
      'createdAt',
      'sumSubKycStatus',
      'levelName',
    ];

    const userDetails = await UserService.fetchUserList(filters, projection, {
      page: Number(page),
      limit: Number(limit),
      sort: sortCriteria,
      search,
    });

    return ResponseHandler.success(res, {
      status: 200,
      error: false,
      message: RES_MSG.USER.USERS_FETCH,
      data: userDetails.data,
    });
  } catch (error) {
    logger.error(error, 'getUserslist Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function getUsersListControllerCsv(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { sort = JSON.stringify({ createdAt: -1 }), startDate = '', endDate = '', isKyc = '' } = req.query;

    // Initialize filters object
    const filters: any = {
      ...(startDate && endDate
        ? {
            createdAt: {
              $gte: new Date(startDate as string),
              $lte: new Date(endDate as string),
            },
          }
        : startDate
        ? { createdAt: { $gte: new Date(startDate as string) } }
        : endDate
        ? { createdAt: { $lte: new Date(endDate as string) } }
        : {}),
    };

    // Parse sort criteria
    // let sortCriteria = JSON.parse(sort as string);

    // Define the projection fields
    const projection = [
      'name',
      'email',
      'userImage',
      'kycStatus',
      'mobile',
      'isKyc',
      'mainInformation.nationality',
      'institutions.companyInformation',
      'isActive',
      'userType',
      'countryCode',
      'sumSubKycStatus',
      'levelName',
      'createdAt',

      'startDate',
      'endDate',
    ];

    // Fetch user list with the applied filters and sorting
    const userDetails = await UserService.fetchUserListcsv(filters, projection);

    return ResponseHandler.success(res, {
      status: 200,
      error: false,
      message: RES_MSG.USER.USERS_FETCH,
      data: userDetails.data,
    });
  } catch (error) {
    logger.error(error, 'getUserslist Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function getIssuerListControllerCsv(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { sort = JSON.stringify({ createdAt: -1 }), startDate = '', endDate = '', issuerStatus = '', isKyc = '', userType = '' } = req.query;

    // Initialize filters object
    const filters: any = {
      isKyc: true,
      ...(userType && { userType }),
      ...(issuerStatus
        ? { IssuerStatus }
        : {
            issuerStatus: { $ne: IssuerStatus.NOT_APPLIED },
          }),
      ...(startDate && endDate
        ? {
            createdAt: {
              $gte: new Date(startDate as string),
              $lte: new Date(endDate as string),
            },
          }
        : startDate
        ? { createdAt: { $gte: new Date(startDate as string) } }
        : endDate
        ? { createdAt: { $lte: new Date(endDate as string) } }
        : {}),
    };

    // Parse sort criteria
    // let sortCriteria = JSON.parse(sort as string);

    // Define the projection fields
    const projection = [
      'name',
      'email',
      'userImage',
      'kycStatus',
      'mobile',
      'isKyc',
      'mainInformation.nationality',
      'institutions.companyInformation',
      'isActive',
      'userType',
      'countryCode',
      'sumSubKycStatus',
      'levelName',
      'createdAt',
      'issuerStatus',
      'startDate',
      'endDate',
    ];

    // Fetch user list with the applied filters and sorting
    const userDetails = await UserService.fetchUserListcsv(filters, projection);

    return ResponseHandler.success(res, {
      status: 200,
      error: false,
      message: RES_MSG.USER.USERS_FETCH,
      data: userDetails.data,
    });
  } catch (error) {
    logger.error(error, 'getUserslist Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

// export async function getManageIssuerListCsv(
//   req: Request,
//   res: Response
// ): Promise<PromiseResolve> {
//   try {
//     const {
//       sort = JSON.stringify({ createdAt: -1 }),
//       startDate = "",
//       endDate = "",
//     } = req.query;

//     // Initialize filters object
//     const filters: any = {};

//     // Add date filtering based on startDate and endDate
//     if (startDate && endDate) {
//       filters.createdAt = {
//         $gte: new Date(startDate as string),
//         $lte: new Date(endDate as string),
//       };
//     } else if (startDate) {
//       filters.createdAt = { $gte: new Date(startDate as string) };
//     } else if (endDate) {
//       filters.createdAt = { $lte: new Date(endDate as string) };
//     }

//     // Parse sort criteria
//     // let sortCriteria = JSON.parse(sort as string);

//     // Define the projection fields
//     const projection = ["name", "email", "_id", "wallets", "userImage", "offeringStatusCounts"];

//     // Fetch user list with the applied filters and sorting
//     const userDetails = await UserService.fetchUserListWithOfferingscsv(
//       filters,
//       projection,
//     );

//     return ResponseHandler.success(res, {
//       status: 200,
//       error: false,
//       message: RES_MSG.USER.USERS_FETCH,
//       data: userDetails.data,
//     });
//   } catch (error) {
//     logger.error(error, "getUserslist Error");
//     return ResponseHandler.error(res, {
//       message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
//       status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
//       error: true,
//     });
//   }
// }

export async function getManageIssuerListCsv(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { page = 1, limit = 10, sort, search = '', issuerStatus = '', startDate, endDate } = req.query;

    const filters: any = {
      isKyc: true,
      isIssuer: true,
      ...(issuerStatus && { issuerStatus }),
      ...(startDate && endDate
        ? {
            createdAt: {
              $gte: new Date(startDate as string),
              $lte: new Date(endDate as string),
            },
          }
        : startDate
        ? { createdAt: { $gte: new Date(startDate as string) } }
        : endDate
        ? { createdAt: { $lte: new Date(endDate as string) } }
        : {}),
    };

    const projection = ['name', 'email', '_id', 'wallets', 'userImage', 'offeringStatusCounts', 'createdAt', 'isActive', 'isKyc', 'isIssuer'];

    const userDetails = await UserService.fetchUserListWithOfferingscsv(filters, projection, {
      page: Number(page),
      limit: Number(limit),
      ...(search && { search: search }),
      ...(sort && { sort: JSON.parse(sort as string) }),
    });
    return ResponseHandler.success(res, {
      status: userDetails.status,
      error: false,
      message: RES_MSG?.SUCCESS_MSG.ISSUER_LIST,
      data: userDetails.data,
    });
  } catch (error: any) {
    logger.error(error, 'getIssuerOfferingController');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function dashboard(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { page = 1, limit = 10, sort = JSON.stringify({ createdAt: -1 }), startDate, endDate, issuerStatus = '' } = req.query;

    // Add date filtering based on startDate and endDate
    const filters = {
      isKyc: true,
      isIssuer: true,
      ...(issuerStatus && { issuerStatus }),
    };

    let sortCriteria = JSON.parse(sort as string);
    const projection = [
      'projectDetails.offeringName',
      'projectDetails.tokenTicker',
      'projectDetails.startDate',
      'projectDetails.endDate',
      'projectDetails.authorizedCountries',
      'createdAt',
      'createdBy',
      'status',
    ];

    const userDetails = await OfferingService.dashboardList(filters, projection, {
      page: Number(page),
      limit: Number(limit),
      sort: sortCriteria,
    });

    return ResponseHandler.success(res, {
      status: 200,
      error: false,
      message: RES_MSG?.SUCCESS_MSG.ISSUER_LIST,
      data: userDetails.data,
    });
  } catch (error: any) {
    logger.error(error, 'getIssuerList Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function topInvestors(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const pipeline: any = [
      // Step 1: Filter only MINTED orders
      {
        $match: { status: orderStatusEnum.MINTED }, // Ensure this is the correct enum or string value
      },

      // Step 2: Compute the total investment per user
      {
        $group: {
          _id: '$userId',
          totalInvestment: { $sum: { $toDouble: '$amount' } }, // Convert and sum
        },
      },

      // Step 3: Sort users by total investment (Descending) & limit to top 5
      { $sort: { totalInvestment: -1 } },
      { $limit: 5 },

      // Step 4: Compute the grand total investment of all users (inside $facet)
      {
        $facet: {
          topInvestors: [
            // This will process only the top 5 users
            {
              $lookup: {
                from: 'users',
                localField: '_id',
                foreignField: '_id',
                as: 'userData',
                pipeline: [{ $project: { name: 1, email: 1, userImage: 1 } }],
              },
            },
            // Convert userData from array to a single object
            {
              $project: {
                userId: '$_id',
                totalInvestment: 1,
                userData: { $arrayElemAt: ['$userData', 0] },
              },
            },
          ],
          grandTotal: [{ $group: { _id: null, grandTotalInvestment: { $sum: '$totalInvestment' } } }],
        },
      },

      // Step 5: Merge the grand total investment into each investor record
      {
        $unwind: '$grandTotal',
      },

      // Step 6: Compute the invested percentage for each user
      {
        $project: {
          topInvestors: {
            $map: {
              input: '$topInvestors',
              as: 'investor',
              in: {
                userId: '$$investor.userId',
                name: '$$investor.userData.name',
                email: '$$investor.userData.email',
                userImage: '$$investor.userData.userImage',
                totalInvestment: '$$investor.totalInvestment',
                investedPercentage: {
                  $multiply: [
                    {
                      $cond: {
                        if: { $gt: ['$grandTotal.grandTotalInvestment', 0] }, // Check if grandTotalInvestment > 0
                        then: { $divide: ['$$investor.totalInvestment', '$grandTotal.grandTotalInvestment'] },
                        else: 0, // If 0, return 0% to avoid division error
                      },
                    },
                    100,
                  ],
                },
              },
            },
          },
        },
      },

      // Step 7: Unwind topInvestors for final output
      {
        $unwind: '$topInvestors',
      },

      {
        $replaceRoot: { newRoot: '$topInvestors' },
      },
    ];

    const result = await OrderSchema.aggregate(pipeline);
    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.USER.USERS_FETCH,
      data: {
        topInvestors: result || [],
      },
    });
  } catch (error: any) {
    logger.error(error, 'topInvestors Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function user(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    // Aggregation pipeline to get investor count and pending investor count
    const investorAggregation = await userSchema.aggregate([
      {
        $match: { userType: { $nin: [UserType.Admin, UserType.Subadmin] } },
      },
      {
        $group: {
          _id: null,
          investorCount: { $sum: 1 },
          approvedInvestorCount: {
            $sum: {
              $cond: [{ $eq: ['$kycStatus', KycStatus.APPROVED] }, 1, 0],
            },
          },
          rejectedInvestorCount: {
            $sum: {
              $cond: [{ $eq: ['$kycStatus', KycStatus.REJECTED] }, 1, 0],
            },
          },
          pendingInvestorCount: {
            $sum: {
              $cond: [{ $eq: ['$kycStatus', KycStatus.PENDING] }, 1, 0],
            },
          },
          inProgressKycInvestorCount: {
            $sum: {
              $cond: [{ $eq: ['$kycStatus', KycStatus.IN_PROGRESS] }, 1, 0],
            },
          },
          reSubmittedKycInvestorCount: {
            $sum: {
              $cond: [{ $eq: ['$kycStatus', KycStatus.RESUBMIT] }, 1, 0],
            },
          },
        },
      },
    ]);

    // Aggregation pipeline to get issuer counts
    const issuerAggregation = await userSchema.aggregate([
      {
        $match: { userType: { $nin: [UserType.Admin, UserType.Subadmin] } },
      },
      {
        $group: {
          _id: null,
          issuerCount: {
            $sum: { $cond: [{ $eq: ['$issuerStatus', IssuerStatus.APPROVED] }, 1, 0] },
          },
          rejectedIssuerCount: {
            $sum: { $cond: [{ $eq: ['$issuerStatus', IssuerStatus.REJECTED] }, 1, 0] },
          },
          pendingIssuerCount: {
            $sum: { $cond: [{ $and: [{ $eq: ['$isKyc', true] }, { $eq: ['$issuerStatus', 'PENDING'] }] }, 1, 0] },
          },
        },
      },
    ]);

    // Aggregation pipeline to get total investment amount
    // const investmentAggregation = await OrderSchema.aggregate([
    //   {
    //     $match: { status: 'MINTED' },
    //   },
    //   {
    //     $group: {
    //       _id: null,
    //       totalAmount: { $sum: '$amount' },
    //     },
    //   },
    //   {
    //     $project: {
    //       _id: 0,
    //       totalAmount: 1,
    //     },
    //   },
    // ]);

    const investmentAggregation = await OrderSchema.aggregate([
      {
        $match: { status: 'MINTED' },
      },
      {
        $addFields: {
          amountAsNumber: { $toDouble: '$amount' }, // Convert amount to a number
        },
      },
      {
        $group: {
          _id: null,
          totalAmount: { $sum: '$amountAsNumber' },
        },
      },
      {
        $project: {
          _id: 0,
          totalAmount: 1,
        },
      },
    ]);

    // Extracting values from aggregations
    const totalAmount = investmentAggregation.length > 0 ? investmentAggregation[0].totalAmount : 0;

    // Returning response
    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG?.SUCCESS_MSG.INVESTOR_COUNT_SUCCESS,
      data: {
        totalAmount,
        ...investorAggregation[0],
        ...issuerAggregation[0],
      },
    });
  } catch (error: any) {
    logger.error(error, 'user API Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise < PromiseResolve >}
 */
export async function getIssuerController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { page = 1, limit = 10, sort = JSON.stringify({ createdAt: -1 }), issuerStatus = '', userType = '', search = '', countryCode = '' } = req.query;
    const disallowedSymbolsRegex = /[!+#$%^*(),?":{}|<>]/;
    const validateSearchResult = await CommonHelper.validateStringRegex(search as string, disallowedSymbolsRegex, RES_MSG.ERROR_MSG.DISALLOWED_SYMBOLS);

    // If validation fails, return the error response immediately
    if (validateSearchResult.error) {
      return ResponseHandler.error(res, {
        status: validateSearchResult.status,
        error: true,
        message: validateSearchResult.message,
      });
    }
    const filters = {
      isKyc: true,
      ...(issuerStatus
        ? { issuerStatus }
        : {
            issuerStatus: { $ne: IssuerStatus.NOT_APPLIED },
          }),
      ...(userType && { userType }),
      ...(countryCode && { countryCode }),
    };

    let sortCriteria = JSON.parse(sort as string);
    const projection = [
      'name',
      'email',
      'userImage',
      'kycStatus',
      'mobile',
      'isIssuer',
      'issuerStatus',
      'issuerReason',
      'mainInformation.nationality',
      'isActive',
      'userType',
      'countryCode',
      'institutions.companyInformation',
      'sumSubKycStatus',
    ];

    const userDetails = await UserService.fetchUserList(filters, projection, {
      page: Number(page),
      limit: Number(limit),
      sort: sortCriteria,
      search,
    });

    return ResponseHandler.success(res, {
      status: 200,
      error: false,
      message: RES_MSG?.SUCCESS_MSG.ISSUER_LIST,
      data: userDetails.data,
    });
  } catch (error: any) {
    logger.error(error, 'getIssuerList Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise < PromiseResolve >}
 */
export async function approveIssuer(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { email, issuerStatus, issuerReason } = req.body;

    // Fetch specific user details
    const userDetails = await UserService.fetchUserDetails({ email }, ['name', 'email', 'kycStatus', 'isActive', 'issuerStatus']);
    const name = userDetails?.data?.name ?? user;
    if (userDetails?.data?.issuerStatus === issuerStatus) {
      throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.BAD_REQUEST);
    }

    const payload = { email, issuerStatus, issuerReason };
    // gRPC call to approve issuer
    userClient.client.approveIssuer(payload, async (error: any, response: any) => {
      if (error || response?.error) {
        const errorMessage = error?.message || response?.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR;
        const errorStatus = error?.status || RESPONSES.INTERNAL_SERVER_ERROR;

        logger.error('gRPC Error:', error || response);
        return ResponseHandler.error(res, {
          status: errorStatus,
          error: true,
          message: errorMessage,
        });
      }

      // Update user's issuer status
      const updatedUser = await userSchema.findOneAndUpdate(
        { email },
        {
          issuerStatus,
          isIssuer: issuerStatus === IssuerStatus.APPROVED ? true : false,
          issuerReason,
        },
        { new: true }, // Return updated document
      );
      if (issuerStatus == IssuerStatus.REJECTED) {
        const emailDetail = {
          name,
          date: Date.now(),
        };
        emailHelper.sendEmailTemplate(email, 'rejectIssuer', emailDetail);
      }
      if (issuerStatus == IssuerStatus.APPROVED) {
        const emailDetail = {
          name,
          reason: issuerReason,
          date: Date.now(),
        };
        emailHelper.sendEmailTemplate(email, 'becomeanissuer', emailDetail);
      }
      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.UPDATE_USER,
        data: issuerStatus,
      });
    });
  } catch (error: any) {
    logger.error(error, 'approveIssuer Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise < PromiseResolve >}
 */
export async function getUserdata(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { userId }: any = req.query;
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return ResponseHandler.error(res, {
        message: 'invalid id',
        status: RESPONSES.BAD_REQUEST,
        error: true,
      });
    }

    const objectId = new mongoose.Types.ObjectId(userId);
    const user = await userSchema.findById(objectId).exec();
    if (!user) {
      return ResponseHandler.error(res, {
        message: RES_MSG.ERROR_MSG.USER_NOT_FOUND,
        status: 400,
        error: true,
      });
    }

    // Fetch user detail data based_id on userId
    // const userDetail = await UserDetailsSchema.findById(objectId).exec();

    // Combine or structure the data as needed
    const combinedData = {
      user: user?.toObject(),
      // details: userDetail ? userDetail.toObject() : null
    };
    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
      data: combinedData,
    });
  } catch (error: any) {
    logger.error(error, 'getUserslist Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<PromiseResolve>}
 */
export async function forgotPasswordController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { email } = req.body;
    const userDetails: PromiseResolve = await UserService.fetchUserDetails({
      email,
    });
    if (userDetails.error)
      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.ERROR_MSG.FORGOT_CRED,
      });
    const otpResponse = await CommonHelper.sendOTP(userDetails.data._id, otpType.FORGOT, userDetails.data.email);
    if (otpResponse.error) throw new CustomError(otpResponse.message, otpResponse.status);
    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.ERROR_MSG.FORGOT_CRED,
    });
  } catch (error: any) {
    logger.error(error, 'Error in forgotPasswordController');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {IUserModel} req
 * @returns {Promise<PromiseResolve>}
 */
export async function changePasswordController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const email = req.userInfo.email;
    const { password, oldPassword } = req.body;
    if (password == oldPassword) throw new CustomError(RES_MSG.ERROR_MSG.NEW_PASSWORD_SAME_AS_CURRENT_PASSWORD, RESPONSES.BAD_REQUEST);
    const userDetails: PromiseResolve = await UserService.fetchUserDetails({
      email,
    });
    if (userDetails.error) throw new CustomError(RES_MSG.COMMON.NO_USER, RESPONSES.UN_AUTHORIZED);

    const verifyPassResp: PromiseResolve = await CommonHelper.verifyPassword(oldPassword, userDetails.data.password);
    if (verifyPassResp.error) throw new CustomError(RES_MSG.ERROR_MSG.INCORRECT_CURRENT_PASSWORD, RESPONSES.BAD_REQUEST);

    const recentPasswordsResp: PromiseResolve = await UserService.fetchRecentPasswords(userDetails.data._id);

    // Check if new password matches any of the recent passwords
    if (!recentPasswordsResp?.error) {
      for (const recentPassword of recentPasswordsResp.data.passwords) {
        const isMatch = await bcrypt.compare(password, recentPassword);
        if (isMatch) {
          throw new CustomError(RES_MSG.ERROR_MSG.PASSWORD_RECENTLY_USED, RESPONSES.BAD_REQUEST);
        }
      }
    }
    const updateUserResp: PromiseResolve = await UserService.updateUserDetails({ password }, { email });

    if (!updateUserResp.error) {
      // Add the new password to the password history
      await UserService.addPassword(userDetails.data._id, updateUserResp.data.password);
      await RedisHelper.deleteKey(`accessToken:${email}`);
      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.PASSWORD_RESET_SUCCESS,
      });
    }
  } catch (error) {
    logger.error(error, 'changePasswordController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<PromiseResolve>}
 */
export async function resetPasswordController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { newPassword } = req.body;
    const { email } = req.body.user;
    const userDetails: PromiseResolve = await UserService.fetchUserDetails({ email }, ['email', '_id'], []);
    if (userDetails.error) throw new CustomError(RES_MSG.COMMON.NO_USER, RESPONSES.UN_AUTHORIZED);
    const recentPasswordsResp: PromiseResolve = await UserService.fetchRecentPasswords(userDetails.data._id);
    if (!recentPasswordsResp.error) {
      for (const recentPassword of recentPasswordsResp.data.passwords) {
        const isMatch = await bcrypt.compare(newPassword, recentPassword);
        if (isMatch) {
          throw new CustomError(RES_MSG.ERROR_MSG.PASSWORD_RECENTLY_USED, RESPONSES.BAD_REQUEST);
        }
      }
    }
    const updateUserResp = await UserService.updateUserDetails({ password: newPassword }, { email });
    if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);
    await RedisHelper.deleteKey(`forgetToken:${email}`);
    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.SUCCESS_MSG.PASSWORD_RESET_SUCCESS,
    });
  } catch (error: any) {
    logger.error(error, 'Error in resetPassword');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}
/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise <PromiseResolve>}
 */
export async function logOutController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { email, userId } = req.userInfo;
    await RedisHelper.deleteKey(`accessToken:${email}`);
    await RedisHelper.deleteKey(`2FA_${userId}`);

    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.SUCCESS_MSG.LOGOUT,
    });
  } catch (error: any) {
    logger.error(error, 'logOut Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<void>}
 */
export async function enable2FAController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { userId, email } = req.userInfo;
    const secret = authenticator.generateSecret();
    const otpAuth = authenticator.keyuri(email, CONFIG.PROJECT.NAME, secret);
    const qrCode = await toDataURL(otpAuth);
    // await RedisHelper.setString(twoFactorSecretKey, secret, CONFIG.REDIS.OTP_EXPIRY)
    const userDetails: PromiseResolve = await UserService.fetchUserDetails({
      email,
    });
    if (userDetails.data.is2FAActive) throw new CustomError(RES_MSG.COMMON.BAD_REQUEST, RESPONSES.BAD_REQUEST);
    const updateUserResp = await UserService.updateUserDetails({ twoFASecret: secret }, { _id: userId });
    if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);
    return ResponseHandler.success(res, {
      status: RESPONSES.CREATED,
      error: false,
      message: RES_MSG.TWO_FA.CREATED,
      data: { qrCode, secret },
    });
  } catch (error) {
    logger.error(error, 'enable2FAController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<void>}
 */
export async function forgot2FAController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { userId } = req.body.user;
    const userDetails: PromiseResolve = await UserService.fetchUserDetails({ _id: userId }, [], ['kycDetails', 'wallets', 'isIssuer', 'issuerStatus', 'createdAt', 'updatedAt']);
    if (userDetails.error) throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
    const user = userDetails?.data;
    const secret = authenticator.generateSecret();
    // await RedisHelper.setString(twoFactorSecretKey, secret, CONFIG.REDIS.OTP_EXPIRY)
    const updateUserResp = await UserService.updateUserDetails({ twoFASecret: secret }, { _id: userId });
    if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);
    const otpAuth = authenticator.keyuri(user?.email, CONFIG.PROJECT.NAME, secret);
    const qrCode = await toDataURL(otpAuth);
    return ResponseHandler.success(res, {
      status: RESPONSES.CREATED,
      error: false,
      message: RES_MSG.TWO_FA.FORGOT_2FA,
      data: { qrCode, secret },
    });
  } catch (error) {
    logger.error(error, 'forgot2FAController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<void>}
 */
export async function reSet2FA(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { token } = req.body;

    const isTokenValid: PromiseResolve = await CommonHelper.isValidToken(token);
    if (isTokenValid.error) {
      throw new CustomError(RES_MSG.TWO_FA.TOKEN_EXPIRE, RESPONSES.UN_AUTHORIZED);
    }

    const { userId } = isTokenValid.data;

    const userDetails: PromiseResolve = await UserService.fetchUserDetails({ _id: userId }, [], ['kycDetails', 'wallets', 'isIssuer', 'issuerStatus', 'createdAt', 'updatedAt']);

    if (userDetails.error) {
      throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
    }

    const updateData = {
      is2FAActive: false,
      twoFASecret: '',
    };

    const updateUserResp = await UserService.updateUserDetails(updateData, {
      _id: userId,
    });

    if (updateUserResp.error) {
      throw new CustomError(updateUserResp.message, updateUserResp.status);
    }

    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.TWO_FA.RESET_SUCCESS,
      data: {},
    });
  } catch (error) {
    logger.error(error, 'reSet2FA Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<void>}
 */
export async function verify2FAController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { token } = req.body;
    const { userId } = req.userInfo;
    const userDetails: PromiseResolve = await UserService.fetchUserDetails({ _id: userId }, [], ['kycDetails', 'wallets', 'isIssuer', 'issuerStatus', 'createdAt', 'updatedAt']);
    if (userDetails.error) throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
    const isValidTwoFAToken = authenticator.check(token, userDetails.data.twoFASecret);
    if (!isValidTwoFAToken) {
      throw new CustomError(RES_MSG.TWO_FA.TOKEN_INVALID, RESPONSES.BAD_REQUEST);
    }
    const updateUserResp = await UserService.updateUserDetails({ is2FAActive: true }, { _id: userId });
    if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);
    await RedisHelper.setString(`2FA_${userId}`, 'true', CONFIG.REDIS.REDIS_AUTH_EXPIRE);
    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.TWO_FA.VERIFIED_SUCCESS,
      data: null,
    });
  } catch (error) {
    logger.error(error, 'verify2FAController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

export async function verify2FALcoginController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { code } = req.body;
    const { userId } = req.body.user;
    const userDetails: PromiseResolve = await UserService.fetchUserDetails({ _id: userId }, [], ['kycDetails', 'wallets', 'isIssuer', 'issuerStatus', 'createdAt', 'updatedAt']);
    if (userDetails.error) throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
    const isValidTwoFAToken = authenticator.check(code, userDetails.data.twoFASecret);
    if (!isValidTwoFAToken) {
      throw new CustomError(RES_MSG.TWO_FA.TOKEN_INVALID, RESPONSES.BAD_REQUEST);
    }
    const updateUserResp = await UserService.updateUserDetails({ is2FAActive: true }, { _id: userId });
    if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);
    await RedisHelper.setString(`2FA_${userId}`, 'true', CONFIG.REDIS.REDIS_AUTH_EXPIRE);
    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.TWO_FA.VERIFIED_SUCCESS,
      data: null,
    });
  } catch (error) {
    logger.error(error, 'verify2FAController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<void>}
 */
export async function disable2FAController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { token } = req.body;
    const { userId, email } = req.userInfo;
    const userDetails: PromiseResolve = await UserService.fetchUserDetails({
      email,
    });
    if (userDetails.error) throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
    const isValidTwoFAToken = authenticator.check(token, userDetails.data.twoFASecret);
    if (!isValidTwoFAToken) {
      throw new CustomError(RES_MSG.TWO_FA.TOKEN_INVALID, RESPONSES.BAD_REQUEST);
    }
    const updateUserResp = await UserService.updateUserDetails({ is2FAActive: false }, { _id: userId });
    if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);
    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.TWO_FA.DISABLE_SUCCESS,
      data: null,
    });
  } catch (error) {
    logger.error(error, 'disable2FAController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<void>}
 */
export async function verifyLogin2FAController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { token, otp } = req.body;
    const { userId } = req.body.user;
    const userDetails: PromiseResolve = await UserService.fetchUserDetails({ _id: userId }, [], ['kycDetails', 'wallets', 'isIssuer', 'issuerStatus', 'createdAt', 'updatedAt']);
    const user = userDetails.data;

    if (userDetails.error) throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
    const isValidTwoFAToken = authenticator.check(otp, user.twoFASecret);
    if (!isValidTwoFAToken) {
      throw new CustomError(RES_MSG.TWO_FA.TOKEN_INVALID, RESPONSES.BAD_REQUEST);
    }
    const updateUserResp = await UserService.updateUserDetails({ is2FAActive: true }, { _id: userId });
    if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);

    const tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email, userType: user.userType }, true);
    if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);
    await RedisHelper.setString(`2FA_${userId}`, 'true', CONFIG.REDIS.REDIS_AUTH_EXPIRE);
    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,

      message: RES_MSG.TWO_FA.VERIFIED_SUCCESS,
      data: { ...tokenResp.data, walletAddress },
    });
  } catch (error) {
    logger.error(error, 'verify2FAController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}
/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise <PromiseResolve>}
 */
export async function approve(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    let message: any;
    const { email, kycStatus, kycReason } = req.body;
    const payload = { email, kycStatus, kycReason };

    const userDetails = await UserService.fetchUserDetails({ email }, ['name', 'email', 'kycStatus', 'isActive', 'kycCount', 'userType'], [], false);

    if (userDetails.error) throw new CustomError(RES_MSG.COMMON.NO_USER, RESPONSES.UN_AUTHORIZED);

    const user = userDetails?.data;

    if (user?.isActive == false) {
      throw new CustomError(RES_MSG.COMMON.BLOCK_USER, RESPONSES.BAD_REQUEST);
    }

    if (user?.kycStatus === kycStatus) {
      throw new CustomError(`User is already ${kycStatus}`, RESPONSES.BAD_REQUEST);
    }

    // Prepare the update object with all conditions
    const updateData: any = {
      kycStatus: kycStatus,
      kycReason: kycReason,
      isKyc: false, // Default false, will update later based on gRPC response
    };

    if (kycStatus === 'REJECTED' && user?.kycCount === +kycCount - 1) {
      updateData.isActive = false; // Mark user inactive if rejected
      updateData.kycCount = kycCount;
    }

    // Call gRPC service
    userClient.client.approveKyc(payload, async (error: any, response: any) => {
      if (error) {
        logger.error('gRPC Error:', error);
        return ResponseHandler.error(res, {
          status: RESPONSES.INTERNAL_SERVER_ERROR,
          error: true,
          message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        });
      }

      if (response.error) {
        return ResponseHandler.error(res, {
          message: response.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
          status: RESPONSES.BAD_REQUEST,
          error: true,
        });
      }

      // Update isKyc status based on gRPC response
      updateData.isKyc = response.error ? false : true;

      // Base emailDetail object
      let emailDetail: { name: string; kycReason?: string } = {
        name: user.name,
      };

      if (kycStatus === 'APPROVED') {
        if (user.userType == UserType.Investor) {
          message = RES_MSG.SUCCESS_MSG.KYC_APPROVED;
          emailHelper.sendEmailTemplate(email, 'KYCApproved', emailDetail);
        } else if (user.userType == UserType.Institution) {
          message = RES_MSG.SUCCESS_MSG.KYB_APPROVED;
          emailHelper.sendEmailTemplate(email, 'KYBApproved', emailDetail);
        }
      } else if (kycStatus === 'REJECTED') {
        if (user.userType == UserType.Investor) {
          message = RES_MSG.SUCCESS_MSG.KYC_REJECTED;
          emailDetail.kycReason = kycReason; // Conditionally add kycReason
          emailHelper.sendEmailTemplate(email, 'KYCRejected', emailDetail);
        } else if (user.userType == UserType.Institution) {
          message = RES_MSG.SUCCESS_MSG.KYB_REJECTED;
          emailDetail.kycReason = kycReason; // Conditionally add kycReason
          emailHelper.sendEmailTemplate(email, 'KYBRejected', emailDetail);
        }
      }

      // Single database update call
      const updatedUser = await userSchema.findOneAndUpdate({ email: email }, updateData, { new: true });

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: message,
        data: kycStatus,
      });
    });
  } catch (error: any) {
    logger.error(error, 'logOut Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

export async function getFeeList(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const getFeeList = await feeSchema.find().sort({ createdAt: -1 });

    const role = req?.userInfo.userType;
    if (role != UserType.Admin) {
      throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.CONFLICT);
    }
    let count: any = 0;
    let maxId;

    if (getFeeList.length > 0) {
      maxId = getFeeList.reduce((max, obj) => (parseInt(obj?._id) > parseInt(max) ? obj?._id : max), getFeeList[0]._id);
      count = maxId;
    }

    const user = {
      count,
      getFeeList,
    };
    return ResponseHandler.success(res, {
      status: 200,
      error: false,
      message: RES_MSG.USER.USERS_FETCH,
      data: user,
    });
  } catch (error) {
    logger.error(error, 'getFeeList Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function curentFee(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const getWalletAddress = await userSchema.findOne(
      { email: process.env.EMAIL },
      { walletAddress: 1, _id: 0 }, // Only fetch walletAddress, exclude _id
    );

    const role = req?.userInfo.userType;
    if (role !== UserType.Admin) {
      throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.CONFLICT);
    }

    const details = {
      walletAddress: getWalletAddress?.walletAddress,
    };
    return ResponseHandler.success(res, {
      status: 200,
      error: false,
      message: RES_MSG.USER.DETAILS_FETCH,
      data: details,
    });
  } catch (error) {
    logger.error(error, 'getFeeList Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise <PromiseResolve>}
 */
export async function unblock(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { email, isActive } = req.body;

    // Fetch user details and return specific fields
    const userDetails = await UserService.fetchUserDetails({ email }, ['name', 'email', 'kycStatus', 'isActive']);

    if (!userDetails.data) {
      throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.BAD_REQUEST);
    }

    const currentIsActive = userDetails.data.isActive;
    if (String(currentIsActive) === String(isActive)) {
      throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.BAD_REQUEST);
    }

    const payload = {
      email,
      isActive,
      kycCount: 0,
    };

    // gRPC call to unblock the user
    userClient.client.unblockUser(payload, async (error: any, response: any) => {
      if (error) {
        logger.error('gRPC Error:', error);
        return ResponseHandler.error(res, {
          status: RESPONSES.INTERNAL_SERVER_ERROR,
          error: true,
          message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        });
      }

      if (response.error) {
        return ResponseHandler.error(res, {
          status: RESPONSES.BAD_REQUEST,
          error: true,
          message: response.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        });
      }

      // Update user's active status and reset kycCount if activated
      const updatedUser = await userSchema.findOneAndUpdate(
        { email },
        {
          isActive,
          ...(isActive === 'true' && { kycCount: 0 }), // Conditionally reset kycCount
        },
        { new: true }, // Return the updated document
      );

      const emailDetails = { name: userDetails.data.name };
      let message;

      if (isActive === 'true') {
        emailHelper.sendEmailTemplate(email, 'accountUnBlock', emailDetails);
        message = RES_MSG.COMMON.ADMIN_UNBLOCK_USER;
      } else {
        emailHelper.sendEmailTemplate(email, 'accountBlocked', emailDetails);
        await RedisHelper.deleteKey(`accessToken:${email}`);
        message = RES_MSG.COMMON.ADMIN_BLOCK_USER;
      }

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message,
        data: isActive,
      });
    });
  } catch (error: any) {
    logger.error(error, 'unblock Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * uploadDocs.
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<PromiseResolve>}
 */
export async function uploadDocs(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const file: Express.Multer.File = req.file;
    let currentOffering;
    const { documentType, offeringId: rowOfferingId } = req.body;
    const offeringId = rowOfferingId ? new Types.ObjectId(rowOfferingId) : null;
    if (offeringId) {
      // Fetch current offering if offeringId exists
      currentOffering = await OfferingService.fetchOfferingDetails({
        _id: new Types.ObjectId(offeringId),
      });
      if (currentOffering.error) {
        throw new CustomError(currentOffering.message, currentOffering.status);
      }
    }
    if (!file) {
      return ResponseHandler.error(res, {
        status: RESPONSES.BAD_REQUEST,
        error: true,
        message: RES_MSG.ERROR_MSG.INVALID_FILE,
      });
    }
    let uploadFileRes: PromiseResolve;
    if (offeringId) {
      uploadFileRes = await CloudHelper.uploadFiles(currentOffering.data.userId.toString(), file, documentType, DocumentFolderTypesEnum.OFFERING, offeringId.toString());
    } else {
      uploadFileRes = await CloudHelper.uploadFiles(currentOffering.data.userId.toString(), file, documentType, DocumentFolderTypesEnum.USER);
    }

    if (uploadFileRes.error) throw new CustomError(uploadFileRes.message || RES_MSG.COMMON.BAD_REQUEST, uploadFileRes.status || RESPONSES.BAD_GATEWAY);

    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.SUCCESS_MSG.DOCS_UPLOADED_SUCCESS,
      data: uploadFileRes.data,
    });
  } catch (error: any) {
    logger.error(error, 'uploadDocsController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

export async function singleOfferingReportController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { offeringId, page = 1, limit = 10, sort = JSON.stringify({ createdAt: -1 }), search = '' }: any = req.query;
    let sortCriteria = JSON.parse(sort as string);
    // const offeringId = new Types.ObjectId(req.query.offeringId);
    const result: PromiseResolve = await UserService.singleOfferingReport(
      { offeringId },
      {
        page: Number(page),
        limit: Number(limit),
        sort: sortCriteria,
        search,
      },
    );
    if (result.error) {
      throw new CustomError(result.message, result.status);
    }
    return ResponseHandler.success(res, {
      status: result.status || RESPONSES.SUCCESS,
      error: false,
      message: result.message || RES_MSG.USER.USERS_FETCH,
      data: result.data || [],
    });
  } catch (error) {
    logger.error(error, 'singleOfferingReportController Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

// Class-based wrapper - same functions, just wrapped in class object
class UserAuthenticationController {
  public loginController = loginController;
  public verifyController = verifyController;
  public reSendOtpController = reSendOtpController;
  public getUsersListController = getUsersListController;
  public getUsersListControllerCsv = getUsersListControllerCsv;
  public getIssuerListControllerCsv = getIssuerListControllerCsv;
  public getManageIssuerListCsv = getManageIssuerListCsv;
  public dashboard = dashboard;
  public topInvestors = topInvestors;
  public user = user;
  public getIssuerController = getIssuerController;
  public approveIssuer = approveIssuer;
  public getUserdata = getUserdata;
  public forgotPasswordController = forgotPasswordController;
  public changePasswordController = changePasswordController;
  public resetPasswordController = resetPasswordController;
  public logOutController = logOutController;
  public enable2FAController = enable2FAController;
  public forgot2FAController = forgot2FAController;
  public reSet2FA = reSet2FA;
  public verify2FAController = verify2FAController;
  public verify2FALcoginController = verify2FALcoginController;
  public disable2FAController = disable2FAController;
  public verifyLogin2FAController = verifyLogin2FAController;
  public approve = approve;
  public getFeeList = getFeeList;
  public curentFee = curentFee;
  public unblock = unblock;
  public uploadDocs = uploadDocs;
  public singleOfferingReportController = singleOfferingReportController;
}

export default new UserAuthenticationController();
