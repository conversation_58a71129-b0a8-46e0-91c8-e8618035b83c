import { JoiValidationResult, otpType, UserType } from '../../utils/common.interface';
import * as Joi from 'joi';
import * as joiOptions from '../../helpers/joiError.filter.helper';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import { parsePhoneNumberFromString, getCountries, getCountryCallingCode } from 'libphonenumber-js';
import { namePattern, otpPattern, passwordPattern } from '../../utils/constant';
import logger from '../../helpers/logging/logger.helper';

class UserValidation {
  /**
   * @returns {Promise<JoiValidationResult>}
   * @memberof AuthValidation
   */
  async singUpValidation(params: object): Promise<JoiValidationResult> {
    try {
      const countryCodeMap: { [key: string]: string } = {};
      getCountries().forEach((country) => {
        countryCodeMap[`+${getCountryCallingCode(country)}`] = country;
      });
      const userTypeValues = Object.values(UserType);

      const schema: Joi.Schema = Joi.object({
        name: Joi.string().trim().max(200).required().pattern(namePattern).label('Name').messages({
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
          'string.max': '{#label} must be at most 200 characters long',
          'string.pattern.base': '{#label} must contain only alphabetic characters and spaces',
        }),
        email: Joi.string().trim().max(200).email().required().lowercase().label('Email').messages({
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
          'string.max': '{#label} must be at most 200 characters long',
        }),
        password: Joi.string().trim().min(8).max(50).pattern(passwordPattern).required().label('Password').messages({
          'string.empty': '{#label} is required',
          'string.min': '{#label} must contain at least 8 characters.',
          'string.max': '{#label} must contain at most 50 characters.',
          'string.pattern.base': '{#label} must contain at least one uppercase letter, one lowercase letter, one number, and one special character.',
          'any.required': '{#label} is required',
        }),
        countryCode: Joi.string()
          .trim()
          .required()
          .label('Country Code')
          .messages({
            'string.empty': '{#label} is required',
            'any.required': '{#label} is required',
          })
          .custom((value, helpers) => {
            if (!countryCodeMap[value]) {
              return helpers.error('any.invalid', { label: 'Country Code' });
            }
            return value;
          }),
        mobile: Joi.string()
          .trim()
          .required()
          .label('Mobile Number')
          .messages({
            'string.empty': '{#label} is required',
            'any.required': '{#label} is required',
          })
          .custom((value, helpers) => {
            const { countryCode } = helpers.state.ancestors[0];
            const phoneNumber = parsePhoneNumberFromString(value, {
              defaultCallingCode: countryCode.replace('+', ''),
            });
            if (!phoneNumber || !phoneNumber.isValid()) {
              return helpers.error('any.invalid', { label: 'Mobile Number' });
            }
            return value;
          }),
        userType: Joi.string()
          .trim()
          .valid(...userTypeValues)
          .required()
          .label('User Type')
          .messages({
            'string.empty': '{#label} is required',
            'any.only': '{#label} must be a valid user type',
            'any.required': '{#label} is required',
          }),
      });

      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }
      return {
        error: false,
        value,
      };
    } catch (error: any) {
      logger.error(error, 'singUpValidation Error');
      return {
        error: true,
        value: '',
        message: RES_MSG.COMMON.SOMETHING_WRONG,
      };
    }
  }

  /**
   * @returns {Promise<JoiValidationResult>}
   * @memberof AuthValidation
   */
  async loginValidation(params: object): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({
        email: Joi.alternatives()
          .try(
            Joi.string().trim().email().lowercase().label('Email').messages({
              'string.empty': '{#label} is required',
              'any.required': '{#label} is required',
              'string.email': 'Please enter valid {#label} address',
            }),
          )
          .required(),
        password: Joi.string().trim().required().label('Password').messages({
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
      });

      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }
      return {
        error: false,
        value,
      };
    } catch (error: any) {
      logger.error(error, 'loginValidation Error');
      return {
        error: true,
        value: '',
        message: RES_MSG.COMMON.SOMETHING_WRONG,
      };
    }
  }

  /**
   * @returns {Promise<JoiValidationResult>}
   * @memberof AuthValidation
   */
  async socialLoginValidation(params: any): Promise<JoiValidationResult> {
    try {
      const userTypeValues = Object.values(UserType);

      const schema: Joi.Schema = Joi.object({
        token: Joi.string().required().trim(),
        userType: Joi.string()
          .trim()
          .valid(...userTypeValues)
          .required()
          .label('User Type')
          .messages({
            'string.empty': '{#label} is required',
            'any.only': '{#label} must be a valid user type',
            'any.required': '{#label} is required',
          }),
      });

      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }
      return {
        error: false,
        value: value,
      };
    } catch (error: any) {
      logger.error(error, 'verifyTokenValidation Validation');
      return {
        error: true,
        value: '',
        message: RES_MSG.COMMON.SOMETHING_WRONG,
      };
    }
  }

  /**
   * @returns {Promise<JoiValidationResult>}
   * @memberof AuthValidation
   */
  async verificationValidation(params: object): Promise<JoiValidationResult> {
    try {
      const typeValues = Object.values(otpType);
      const schema: Joi.Schema = Joi.object({
        email: Joi.alternatives()
          .try(
            Joi.string().trim().email().lowercase().label('Email').messages({
              'string.empty': '{#label} is required',
              'any.required': '{#label} is required',
              'string.email': 'Please enter valid {#label} address',
            }),
          )
          .required(),
        otp: Joi.string().trim().length(6).pattern(otpPattern).required().label('OTP').messages({
          'string.empty': '{#label} is required',
          'string.length': '{#label} must be exactly 6 digits long.',
          'string.pattern.base': '{#label} must contain only digits from 0 to 9.',
          'any.required': '{#label} is required',
        }),
        type: Joi.string()
          .trim()
          .valid(...typeValues)
          .required()
          .label('OTP Type')
          .messages({
            'string.empty': '{#label} is required',
            'any.only': '{#label} must be a valid otp type',
            'any.required': '{#label} is required',
          }),
      });

      const { error, value } = schema.validate(params, joiOptions.options);

      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }
      return {
        error: false,
        value,
      };
    } catch (error: any) {
      logger.error(error, 'verificationValidation Error');
      return {
        error: true,
        value: '',
        message: RES_MSG.COMMON.SOMETHING_WRONG,
      };
    }
  }

  /**
   * @returns {Promise<JoiValidationResult>}
   * @memberof UserValidation
   */
  async updateProfileValidation(params: object): Promise<JoiValidationResult> {
    try {
      const updateProfileSchema = Joi.object({
        name: Joi.string().trim().required().label('Name').messages({
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
        dob: Joi.date().optional().label('Date of birth').messages({
          'date.base': '{#label} must be a valid date',
        }),
      });

      const { error, value } = updateProfileSchema.validate(params, joiOptions.options);
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }
      return {
        error: false,
        value,
      };
    } catch (error) {
      logger.error(error, 'userUpdateValidation Error');
      return {
        error: true,
        value: '',
        message: RES_MSG.COMMON.SOMETHING_WRONG,
      };
    }
  }

  /**
   * @returns {Promise<JoiValidationResult>}
   * @memberof UserValidation
   */
  async updateKycValidation(params: object): Promise<JoiValidationResult> {
    try {
      const addressSchema = Joi.object({
        address: Joi.string().required().label('Address').messages({
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
        address2: Joi.string().optional().label('Address2'),
        country: Joi.string().required().label('Country').messages({
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
        state: Joi.string().required().label('State').messages({
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
        city: Joi.string().required().label('City').messages({
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
        zipCode: Joi.string().required().label('Zip Code').messages({
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
      });

      const walletSchema = Joi.object({
        type: Joi.string().required().label('Wallet Type').messages({
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
        address: Joi.string().required().label('Wallet Address').messages({
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
      });

      const personalInformationSchema = Joi.object({
        name: Joi.string().required().label('Name').messages({
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
        jobTitle: Joi.string().optional().label('Job Title').messages({
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
        dob: Joi.date().optional().label('Date of Birth').messages({
          'date.base': '{#label} must be a valid date',
        }),
        socialSecurityNumber: Joi.number().required().label('Social Security Number').messages({
          'number.base': '{#label} must be a valid number',
          'any.required': '{#label} is required',
        }),
        citizenship: Joi.string().required().label('Citizenship').messages({
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
        countryCode: Joi.string().optional().label('Country Code').messages({
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
        mobile: Joi.number().optional().label('Mobile Number').messages({
          'number.base': '{#label} must be a valid number',
          'any.required': '{#label} is required',
        }),
        email: Joi.string().lowercase().email().optional().label('Email').messages({
          'string.email': '{#label} must be a valid email',
          'any.required': '{#label} is required',
        }),
      });

      const kycSchema = Joi.object({
        institutions: Joi.object({
          companyInformation: Joi.object({
            name: Joi.string().required().label('Company Name'),
            entityType: Joi.string().required().label('Entity Type'),
            webSite: Joi.string().optional().label('Website'),
            business: Joi.string().required().label('Business Type'),
            sourceOfFunds: Joi.string().required().label('Source of Funds'),
          }),
          address: addressSchema,
        }).optional(),

        primaryContactInfo: Joi.object({
          personalInformation: personalInformationSchema,
          address: addressSchema,
        }).optional(),

        wallets: Joi.array().items(walletSchema).optional(),

        isIdentityVerification: Joi.boolean().optional(),

        documents: Joi.object({
          frontId: Joi.string().required().label('Front ID'),
          backId: Joi.string().required().label('Back ID'),
          companyIdentification: Joi.string().required().label('Company Identification'),
        }).optional(),

        beneficialOwners: Joi.array()
          .items(
            Joi.object({
              personalInformation: personalInformationSchema,
              address: addressSchema,
              identityProof: Joi.object({
                passport: Joi.object({
                  front: Joi.string().optional().label('Passport Front'),
                  back: Joi.string().optional().label('Passport Back'),
                }).optional(),
                driversLicense: Joi.object({
                  front: Joi.string().optional().label('Driver’s License Front'),
                  back: Joi.string().optional().label('Driver’s License Back'),
                }).optional(),
                idCard: Joi.object({
                  front: Joi.string().optional().label('ID Card Front'),
                  back: Joi.string().optional().label('ID Card Back'),
                }).optional(),
              }).optional(),
            }),
          )
          .optional(),

        managementInfo: Joi.array()
          .items(
            Joi.object({
              personalInformation: personalInformationSchema,
              address: addressSchema,
            }),
          )
          .optional(),
      }).min(1);

      const { error, value } = kycSchema.validate(params, joiOptions.options);
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }
      return {
        error: false,
        value,
      };
    } catch (error) {
      logger.error(error, 'userUpdateValidation Error');
      return {
        error: true,
        value: '',
        message: RES_MSG.COMMON.SOMETHING_WRONG,
      };
    }
  }

  /**
   * @returns {Promise<JoiValidationResult>}
   * @memberof UserValidation
   */
  async changePasswordValidation(params: object): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({
        oldPassword: Joi.string().trim().min(8).max(50).required().label('Password').messages({
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
        password: Joi.string().trim().min(8).max(50).required().label('Password').messages({
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
      }).custom((value, helpers) => {
        if (value.password === value.oldPassword) {
          return 'Password must not be the same as Old Password';
        }
        return value;
      });

      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }
      return {
        error: false,
        value,
      };
    } catch (error) {
      logger.error(error, 'userPasswordValidation Error');
      return {
        error: true,
        value: '',
        message: RES_MSG.COMMON.SOMETHING_WRONG,
      };
    }
  }

  /**
       * export enum IssuerStatus {
    PENDING = "PENDING",
    REJECTED = "REJECTED",
    APPROVED = "APPROVED",
    BLOCKED = "BLOCKED",
  }
     * @returns {Promise<JoiValidationResult>}
     * @memberof UserValidation
     */
  async approveIssuervalidation(params: object): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({
        email: Joi.string().lowercase().email().required().label('Email').messages({
          'string.email': '{#label} must be a valid email',
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
        issuerStatus: Joi.string().valid('PENDING', 'APPROVED', 'REJECTED').required().label('Issuer Status').messages({
          'any.only': '{#label} must be one of [requested, approved, rejected]',
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
        issuerReason: Joi.string().optional().label('Reason').messages({
          'string.empty': '{#label} cannot be empty',
        }),
      });

      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }
      return {
        error: false,
        value,
      };
    } catch (error) {
      logger.error(error, 'issuer request Error');
      return {
        error: true,
        value: '',
        message: RES_MSG.COMMON.SOMETHING_WRONG,
      };
    }
  }

  /**
   * @returns {Promise<JoiValidationResult>}
   * @memberof UserValidation
   */

  async approveUservalidation(params: object): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({
        email: Joi.string().lowercase().email().required().label('Email').messages({
          'string.email': '{#label} must be a valid email',
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
        kycStatus: Joi.string().valid('PENDING', 'APPROVED', 'REJECTED').required().label('User Status').messages({
          'any.only': '{#label} must be one of [PENDING, APPROVED, REJECTED]',
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
        kycReason: Joi.string().optional().label('Reason').messages({
          'string.empty': '{#label} cannot be empty',
        }),
      });

      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }
      return {
        error: false,
        value,
      };
    } catch (error) {
      logger.error(error, 'kys user request Error');
      return {
        error: true,
        value: '',
        message: RES_MSG.COMMON.SOMETHING_WRONG,
      };
    }
  }

  async CreateTransferAgent(params: object): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({
        email: Joi.string().lowercase().email().required().label('Email').messages({
          'string.email': '{#label} must be a valid email',
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
        name: Joi.string().required().label('User Status').messages({
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
        walletAddress: Joi.string().required().label('Reason').messages({
          'string.empty': '{#label} cannot be empty',
        }),
      });

      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }
      return {
        error: false,
        value,
      };
    } catch (error) {
      logger.error(error, 'kys user request Error');
      return {
        error: true,
        value: '',
        message: RES_MSG.COMMON.SOMETHING_WRONG,
      };
    }
  }

  async CreateSubAdmin(params: object): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({
        name: Joi.string().required().label('Name').messages({
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
        email: Joi.string().lowercase().email().required().label('Email').messages({
          'string.email': '{#label} must be a valid email',
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),

        password: Joi.string()
          .min(8)
          .max(12)
          .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,12}$/)
          .required()
          .label('Password')
          .messages({
            'string.empty': '{#label} is required',
            'any.required': '{#label} is required',
            'string.min': '{#label} must be at least 8 characters long',
            'string.max': '{#label} must not exceed 12 characters',
            'string.pattern.base': '{#label} must contain at least one uppercase letter, one lowercase letter, one number, and one special character with no spaces',
          }),
        confirmPassword: Joi.string().valid(Joi.ref('password')).required().label('Confirm Password').messages({
          'any.only': '{#label} does not match Password',
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
        walletAddress: Joi.string()
          .pattern(/^0x[a-fA-F0-9]{40}$/)
          .required()
          .label('Wallet Address')
          .messages({
            'string.empty': '{#label} is required',
            'string.pattern.base': '{#label} must be a valid Ethereum address',
            'any.required': '{#label} is required',
          }),
        permission: Joi.array()
          .items(
            Joi.object({
              moduelsId: Joi.string().required().label('Module ID').messages({
                'string.empty': '{#label} is required',
                'any.required': '{#label} is required',
              }),
              read: Joi.boolean().required().label('Read Permission'),
              write: Joi.boolean().required().label('Write Permission'),
            }),
          )
          .required()
          .label('Permissions')
          .messages({
            'array.base': '{#label} must be an array of permission objects',
            'any.required': '{#label} is required',
          }),
      });

      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }
      return {
        error: false,
        value,
      };
    } catch (error) {
      logger.error(error, 'kys user request Error');
      return {
        error: true,
        value: '',
        message: RES_MSG.COMMON.SOMETHING_WRONG,
      };
    }
  }

  async UpdateSubAdmin(params: object): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({
        userId: Joi.string().required().label('User ID').messages({
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
        walletAddress: Joi.string()
          .pattern(/^0x[a-fA-F0-9]{40}$/)
          .required()
          .label('Wallet Address')
          .messages({
            'string.empty': '{#label} is required',
            'string.pattern.base': '{#label} must be a valid Ethereum address',
            'any.required': '{#label} is required',
          }),
        permission: Joi.array()
          .items(
            Joi.object({
              moduelsId: Joi.string().required().label('Module ID').messages({
                'string.empty': '{#label} is required',
                'any.required': '{#label} is required',
              }),
              read: Joi.boolean().required().label('Read Permission'),
              write: Joi.boolean().required().label('Write Permission'),
            }),
          )
          .required()
          .label('Permissions')
          .messages({
            'array.base': '{#label} must be an array of permission objects',
            'any.required': '{#label} is required',
          }),
      });

      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }
      return {
        error: false,
        value,
      };
    } catch (error) {
      logger.error(error, 'kys user request Error');
      return {
        error: true,
        value: '',
        message: RES_MSG.COMMON.SOMETHING_WRONG,
      };
    }
  }

  async BlockTransferAgent(params: object): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({
        email: Joi.string().lowercase().email().required().label('Email').messages({
          'string.email': '{#label} must be a valid email',
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
        isActive: Joi.boolean().required().label('User Status').messages({
          'boolean.base': '{#label} must be either true or false',
          'any.required': '{#label} is required',
        }),
      });

      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }
      return {
        error: false,
        value,
      };
    } catch (error) {
      logger.error(error, 'kys user request Error');
      return {
        error: true,
        value: '',
        message: RES_MSG.COMMON.SOMETHING_WRONG,
      };
    }
  }

  /**
   * @returns {Promise<JoiValidationResult>}
   * @memberof AuthValidation
   */
  async emailValidation(params: any): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({
        email: Joi.string().trim().email().required().lowercase().label('Email').messages({
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
      });
      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }
      return {
        error: false,
        value: value,
      };
    } catch (error: any) {
      logger.error(error, 'email Validation failed');
      return {
        error: true,
        value: '',
        message: RES_MSG.COMMON.SOMETHING_WRONG,
      };
    }
  }

  /**
   * @returns {Promise<JoiValidationResult>}
   * @memberof AuthValidation
   */
  async resetPasswordValidation(params: any): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({
        token: Joi.string().required().trim(),
        newPassword: Joi.string().trim().min(8).max(50).required().label('Password').messages({
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
      });
      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }
      return {
        error: false,
        value: value,
      };
    } catch (error: any) {
      logger.error(error, 'reset PasswordValidation');
      return {
        error: true,
        value: '',
        message: RES_MSG.COMMON.SOMETHING_WRONG,
      };
    }
  }

  /**
   * @returns {Promise<JoiValidationResult>}
   * @memberof AuthValidation
   */
  async verifyTokenValidation(params: any): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({
        token: Joi.string().required().trim(),
      });

      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }
      return {
        error: false,
        value: value,
      };
    } catch (error: any) {
      logger.error(error, 'verifyTokenValidation Validation');
      return {
        error: true,
        value: '',
        message: RES_MSG.COMMON.SOMETHING_WRONG,
      };
    }
  }

  /**
   * @returns {Promise<JoiValidationResult>}
   * @memberof AuthValidation
   */
  async resendOtpValidation(params: any): Promise<JoiValidationResult> {
    try {
      const typeValues = Object.values(otpType);

      const schema: Joi.Schema = Joi.object({
        email: Joi.string().trim().email().required().lowercase().label('Email').messages({
          'string.empty': '{#label} is required',
          'any.required': '{#label} is required',
        }),
        type: Joi.string()
          .trim()
          .valid(...typeValues)
          .required()
          .label('OTP Type')
          .messages({
            'string.empty': '{#label} is required',
            'any.only': '{#label} must be a valid otp type',
            'any.required': '{#label} is required',
          }),
      });
      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }
      return {
        error: false,
        value: value,
      };
    } catch (error: any) {
      logger.error(error, 'forgotPassword Validation failed');
      return {
        error: true,
        value: '',
        message: RES_MSG.COMMON.SOMETHING_WRONG,
      };
    }
  }

  /**
   * @returns {Promise<JoiValidationResult>}
   * @memberof AuthValidation
   */
  async verify2FAValidation(params: any): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({
        token: Joi.string().required().trim(),
        otp: Joi.string().trim().length(6).pattern(otpPattern).required().label('OTP').messages({
          'string.empty': '{#label} is required',
          'string.length': '{#label} must be exactly 6 digits long.',
          'string.pattern.base': '{#label} must contain only digits from 0 to 9.',
          'any.required': '{#label} is required',
        }),
      });

      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }
      return {
        error: false,
        value: value,
      };
    } catch (error: any) {
      logger.error(error, 'verifyTokenValidation Validation');
      return {
        error: true,
        value: '',
        message: RES_MSG.COMMON.SOMETHING_WRONG,
      };
    }
  }

  /**
   * @returns {Promise<JoiValidationResult>}
   * @memberof AuthValidation
   */
  async docsValidation(params: any): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({
        offeringId: Joi.string().optional(),
        documentType: Joi.when('offeringId', {
          is: Joi.exist(),
          then: joiOptions.offeringDocsTypeSchema,
          otherwise: joiOptions.docsTypeSchema,
        }),
      });

      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }
      return {
        error: false,
        value: value,
      };
    } catch (error: any) {
      logger.error(error, 'verifyTokenValidation Validation');
      return {
        error: true,
        value: '',
        message: RES_MSG.COMMON.SOMETHING_WRONG,
      };
    }
  }
}

export default new UserValidation();
