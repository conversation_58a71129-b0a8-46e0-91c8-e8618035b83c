import mongoose, { Schema, Document } from 'mongoose';
import { IssuerStatus, KycStatus, sumSubKycStatusEnum, UserType } from '../../utils/common.interface';
import { RES_MSG } from '../../utils/responseUtils';

export interface IAddress {
  address: string;
  address2?: string;
  country: string;
  state: string;
  city: string;
  zipCode: string;
}
export interface IMainInformation {
  birthPlace?: string;
  nationality?: string;
  nationalIdNumber?: string;
  identificationDocument?: string;
  occupation?: string;
  documentExpiration?: Date;
  dob?: string;
}

export interface IWallet {
  type?: string; // e.g., 'ETH'
  address?: string;
  timestamp: Date;
}

export interface IDocuments {
  frontId?: string;
  backId?: string;
  otherIdentification?: string;
}
export interface IUpdateUserModel {
  name?: string;
  legalFullName?: string;
  onchainID?: string;
  email?: string;
  password?: string;
  countryCode?: string;
  mobile?: string;
  lastLogin?: Date;
  userImage?: string;
  isActive?: boolean;
  kycApplicationId?: string;
  levelName?: string;
  webhookReceived?: string;
  isEmailVerify?: boolean;
  isMobileVerify?: boolean;
  isDeleted?: boolean;
  is2FAActive?: boolean;
  twoFASecret?: string;
  kycReason?: string;
  issuerReason?: string;
  isOtpActive?: boolean;
  walletAddress?: string;
  isSocialMedia?: boolean;
  userType?: UserType;
  dob?: String;
  isKyc?: boolean;
  kycStatus?: KycStatus;
  kycCount?: Number;
  isIssuer?: boolean;
  issuerStatus?: IssuerStatus;
  mainInformation?: IMainInformation;
  wallets?: IWallet[];
  documents?: IDocuments;
  isIdentityVerification?: { status?: boolean };
  kycSteps?: number;
  isFinalSubmission?: boolean;
  sumSubKycStatus?: string;
  offeringStatusEnum?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IUserModel extends Document {
  _id: string;
  name?: string;
  legalFullName?: string;
  email?: string;
  password?: string;
  onchainID?: string;
  walletAddress?: string;
  countryCode?: string;
  mobile?: string;
  userImage?: string;
  isActive?: boolean;
  isEmailVerify?: boolean;
  isMobileVerify?: boolean;
  isDeleted?: boolean;
  is2FAActive?: boolean;
  twoFASecret?: string;
  sumSubKycStatus?: string;
  kycApplicationId?: string;
  levelName?: string;
  kycReason?: string;
  issuerReason?: string;
  isOtpActive?: boolean;
  isSocialMedia?: boolean;
  userType?: UserType;
  dob?: String;
  webhookReceived?: string;
  isKyc?: boolean;
  kycStatus?: KycStatus;
  kycCount?: Number;
  lastLogin?: Date;
  isIssuer?: boolean;
  issuerStatus?: IssuerStatus;
  mainInformation?: IMainInformation;
  wallets: Array<{
    type: string;
    address: string;
    isVerify: boolean;
    timestamp: Date;
  }>;
  documents?: IDocuments;
  isIdentityVerification?: { status?: boolean };
  kycSteps?: number;
  isFinalSubmission?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  institutions?: {
    companyInformation: {
      name: string;
      entityType: string;
      webSite?: string;
      business: string;
      sourceOfFunds: string;
    };
    address: IAddress;
  };
  primaryContactInfo?: {
    personalInformation: {
      name: string;
      jobTitle: string;
      dob?: String;
      socialSecurityNumber: string;
      citizenship: string;
      countryCode: string;
      mobile: number;
      email: string;
    };
    address: IAddress;
  };
  beneficialOwners: Array<{
    personalInformation: {
      name: string;
      dob?: Date;
      socialSecurityNumber: string;
      citizenship: string;
    };
    address: IAddress;
    identityProof: {
      passport?: {
        front?: string;
        back?: string;
      };
      driversLicense?: {
        front?: string;
        back?: string;
      };
      idCard?: {
        front?: string;
        back?: string;
      };
    };
  }>;
  managementInfo?: Array<{
    personalInformation: {
      name: string;
      jobTitle: string;
      dob?: Date;
      socialSecurityNumber: string;
      citizenship: string;
      countryCode: string;
      mobile: number;
      email: string;
    };
    address: IAddress;
  }>;
}
export interface PasswordModel extends Document {
  _id: string;
  passwords: string[];
}

const AddressSchema: Schema<IAddress> = new Schema(
  {
    address: { type: String, required: true },
    address2: { type: String },
    country: { type: String, required: true },
    state: { type: String, required: true },
    city: { type: String, required: true },
    zipCode: { type: String, required: true },
  },
  { _id: false },
);

const UserSchema: Schema<IUserModel> = new Schema(
  {
    name: {
      type: String,
      required: true,
    },
    legalFullName: {
      type: String,
      required: false,
    },
    lastLogin: {
      type: Date,
      default: null,
    },
    email: {
      type: String,
      unique: true,
      index: true,
      required: true,
    },
    password: {
      type: String,
      required: true,
    },
    onchainID: {
      type: String,
      default: null,
    },
    countryCode: {
      type: String,
      required: false,
    },
    mobile: {
      type: String,
      unique: true,
      index: true,
      required: false,
      sparse: true,
    },
    dob: {
      type: String,
      default: null,
    },
    walletAddress: {
      type: String,
      default: null,
      set: (value: string) => (value ? value.toLowerCase() : value),
    },

    userImage: {
      type: String,
      default: null,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isEmailVerify: {
      type: Boolean,
      default: false,
    },
    isMobileVerify: {
      type: Boolean,
      default: false,
    },
    is2FAActive: {
      type: Boolean,
      default: false,
    },
    twoFASecret: {
      type: String,
      default: null,
    },
    kycApplicationId: {
      type: String,
      default: null,
    },
    sumSubKycStatus: {
      type: String,
      default: sumSubKycStatusEnum.NOT_STARTED,
    },
    webhookReceived: {
      type: String,
      default: null,
    },
    levelName: {
      type: String,
      default: null,
    },
    kycReason: {
      type: String,
      default: null,
    },
    issuerReason: {
      type: String,
      default: null,
    },
    isOtpActive: {
      type: Boolean,
      default: true,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
    isSocialMedia: {
      type: Boolean,
      default: false,
    },
    userType: {
      type: String,
      enum: UserType,
      default: null,
    },
    isKyc: {
      type: Boolean,
      default: false,
    },
    kycStatus: {
      type: String,
      enum: KycStatus,
      default: KycStatus.STARTED,
    },
    kycCount: {
      type: Number,
      default: null,
    },
    isIssuer: {
      type: Boolean,
      default: false,
    },

    issuerStatus: {
      type: String,
      enum: IssuerStatus,
      default: null,
    },
    mainInformation: {
      type: {
        birthPlace: { type: String, default: null },
        nationality: { type: String, default: null },
        nationalIdNumber: { type: String, default: null },
        identificationDocument: { type: String, default: null },
        occupation: { type: String, default: null },
        documentExpiration: { type: Date, default: null },
        dob: { type: String, required: false },
      },
      required: false,
    },
    wallets: {
      type: [
        {
          type: { type: String, required: true },
          address: {
            type: String,
            required: true,
            index: true,
            set: (value: string) => (value ? value.toLowerCase() : value),
          },
          isVerify: { type: Boolean, required: false, default: false },
          timestamp: { type: Date, required: false },
        },
      ],
      _id: false,
      default: undefined,
    },
    documents: {
      type: {
        frontId: { type: String, default: null },
        backId: { type: String, default: null },
        otherIdentification: { type: String, default: null },
      },
      required: false,
    },
    isIdentityVerification: {
      status: { type: Boolean, default: true },
    },
    kycSteps: {
      type: Number,
      default: null,
    },
    isFinalSubmission: {
      type: Boolean,
      default: false,
    },
    institutions: {
      type: {
        companyInformation: {
          name: { type: String, default: null },
          entityType: { type: String, default: null },
          webSite: { type: String, default: null },
          business: { type: String, default: null },
          sourceOfFunds: { type: String, default: null },
        },
        address: { type: AddressSchema, required: true },
      },
      default: undefined,
      required: false,
    },
    primaryContactInfo: {
      type: {
        personalInformation: {
          name: { type: String, default: null },
          jobTitle: { type: String, default: null },
          dob: { type: Date, default: null },
          socialSecurityNumber: { type: String, default: null },
          citizenship: { type: String, default: null },
          countryCode: { type: String, default: null },
          mobile: { type: Number, default: null },
          email: { type: String, default: null },
        },
        address: { type: AddressSchema, required: true },
      },
      default: undefined,
      required: false,
    },
    beneficialOwners: {
      type: [
        {
          personalInformation: {
            name: { type: String, required: true },
            dob: { type: Date },
            socialSecurityNumber: { type: String, required: true },
            citizenship: { type: String, required: true },
          },
          address: { type: AddressSchema, required: true },
          identityProof: {
            passport: {
              front: { type: String },
              back: { type: String },
            },
            driversLicense: {
              front: { type: String },
              back: { type: String },
            },
            idCard: {
              front: { type: String },
              back: { type: String },
            },
          },
        },
      ],
      _id: false,
      default: undefined,
      required: false,
    },
    managementInfo: {
      type: [
        {
          personalInformation: {
            name: { type: String, required: true },
            jobTitle: { type: String, required: true },
            dob: { type: Date },
            socialSecurityNumber: { type: String, required: true },
            citizenship: { type: String, required: true },
            countryCode: { type: String, required: true },
            mobile: { type: String, required: false },
            email: { type: String, required: true },
          },
          address: { type: AddressSchema, required: true },
        },
      ],
      _id: false,
      default: undefined,
      required: false,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const PasswordSchema: Schema<PasswordModel> = new Schema(
  {
    passwords: {
      type: [String],
      required: true,
    },
  },
  {
    timestamps: false,
    versionKey: false,
  },
);

// UserSchema.post("save", function (error: any, doc: any, next: any) {
//   if (error.code === 11000) {
//    if (error.keyPattern && error.keyPattern.email) {
//       next(new Error(RES_MSG.USER.USER_ALREADY_EXIST));
//     // } else {
//     //   next(new Error("Duplicate key error."));
//     // }
//   } else {
//     next(error);
//   }
// });

// UserSchema.pre('updateOne', hashPassword);
// UserSchema.pre('findOneAndUpdate', hashPassword);

const userSchema = mongoose.model<IUserModel>('User', UserSchema);
const passwordSchema = mongoose.model<PasswordModel>('Password', PasswordSchema);
export { userSchema, passwordSchema };
