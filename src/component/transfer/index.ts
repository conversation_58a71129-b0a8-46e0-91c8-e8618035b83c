import { Request, Response } from 'express';
import TransferRequestService from './service';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import { ResponseHandler } from '../../helpers/response.helper';

import CustomError from '../../helpers/customError.helper';

import { Types } from 'mongoose';
import OfferingService from '../offerings/service';
import { PromiseResolve, transferStatusEnum } from '../../utils/common.interface';
import TransferRequest from './models/transfer';
import { HttpStatusCode } from 'axios';

export default class TransferRequestController {
  // Create a new transfer request
}
