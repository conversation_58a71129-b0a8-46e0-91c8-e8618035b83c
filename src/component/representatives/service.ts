import { FilterQuery, Types } from 'mongoose';
import { IPagination, PromiseResolve } from '../../utils/common.interface';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import { IRepresentativeService } from './interface';
import { IRepresentative, IUpdateRepresentative, ICreateRepresentative, representativeSchemaModel } from './representative.model';
import * as bcrypt from 'bcrypt';

class RepresentativeService implements IRepresentativeService {
  /**
   * @param {ICreateRepresentative} representativeData
   * @returns {Promise<PromiseResolve>}
   * @memberof RepresentativeService
   */
  async createRepresentative(representativeData: ICreateRepresentative): Promise<PromiseResolve> {
    try {
      const { name, email, password, walletAddress } = representativeData;

      // Check if representative already exists
      const existingRepresentative = await representativeSchemaModel.findOne({
        email: email.toLowerCase(),
      });

      if (existingRepresentative) {
        return {
          status: RESPONSES.CONFLICT,
          error: true,
          message: 'Representative with this email already exists',
        };
      }

      // Hash password if provided
      let hashedPassword;
      if (password) {
        hashedPassword = await bcrypt.hash(password, 10);
      }

      // Create new representative using user schema with userType = 'representative'
      const newRepresentative = new representativeSchemaModel({
        name: name.trim(),
        email: email.toLowerCase().trim(),
        password: hashedPassword,
        userType: 'representative', // Set userType to representative
        kycStatus: 'NOT_STARTED',
        isActive: true,
        isDeleted: false,
        walletAddress: walletAddress?.toLowerCase() || null,
        isEmailVerify: false,
        isMobileVerify: false,
        is2FAActive: false,
        isOtpActive: true,
      });

      const savedRepresentative = await newRepresentative.save();

      // Remove password from response
      const responseData = savedRepresentative.toObject();
      delete responseData.password;

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: 'Representative created successfully',
        data: responseData,
      };
    } catch (error: any) {
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * @param {FilterQuery<IRepresentative>} searchDetails
   * @param {string[]} fields
   * @param {string[]} excludeFields
   * @returns {Promise<PromiseResolve>}
   * @memberof RepresentativeService
   */
  async fetchRepresentativeDetails(searchDetails: FilterQuery<IRepresentative>, fields?: string[], excludeFields?: string[]): Promise<PromiseResolve> {
    try {
      // Add userType filter to ensure we only get representatives
      const query = representativeSchemaModel.findOne({
        ...searchDetails,
        userType: 'representative',
      });

      if (fields && fields.length > 0) {
        const fieldsString = fields.join(' ');
        query.select(fieldsString);
      }

      if (excludeFields && excludeFields.length > 0) {
        const excludeFieldsString = excludeFields.map((field) => `-${field}`).join(' ');
        query.select(excludeFieldsString);
      }

      const representative = await query.lean();

      if (representative) {
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
          data: representative,
        };
      } else {
        return {
          status: RESPONSES.NOTFOUND,
          error: true,
          message: 'Representative not found',
        };
      }
    } catch (error: any) {
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * @param {FilterQuery<IRepresentative>} searchDetails
   * @param {string[]} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof RepresentativeService
   */
  async fetchRepresentativeList(searchDetails: FilterQuery<IRepresentative>, projection?: string[], pagination?: IPagination): Promise<PromiseResolve> {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 }, search } = pagination || {};
      const skip = (page - 1) * limit;

      // Add userType filter to ensure we only get representatives
      let query: any = {
        ...searchDetails,
        userType: 'representative',
      };

      // Add search functionality
      if (search) {
        query.$or = [{ name: { $regex: search, $options: 'i' } }, { email: { $regex: search, $options: 'i' } }];
      }

      const projectionString = projection ? projection.join(' ') : '-password';

      const totalCount = await representativeSchemaModel.countDocuments(query).exec();
      const representatives = await representativeSchemaModel.find(query).select(projectionString).sort(sort).skip(skip).limit(limit).lean().exec();

      const totalPages = Math.ceil(totalCount / limit);

      return {
        data: {
          representatives,
          currentPage: page,
          totalPages,
          totalCount,
          nextPage: page < totalPages ? page + 1 : null,
          previousPage: page > 1 ? page - 1 : null,
        },
        status: RESPONSES.SUCCESS,
        error: false,
        message: 'Representatives fetched successfully',
      };
    } catch (error: any) {
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * @param {string} representativeId
   * @param {IUpdateRepresentative} updateData
   * @returns {Promise<PromiseResolve>}
   * @memberof RepresentativeService
   */
  async updateRepresentative(representativeId: string, updateData: IUpdateRepresentative): Promise<PromiseResolve> {
    try {
      const objectId = new Types.ObjectId(representativeId);

      // Hash password if provided
      if (updateData.password) {
        updateData.password = await bcrypt.hash(updateData.password, 10);
      }

      const updatedRepresentative = await representativeSchemaModel.findByIdAndUpdate(objectId, updateData, { new: true }).select('-password').lean();

      if (updatedRepresentative) {
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: 'Representative updated successfully',
          data: updatedRepresentative,
        };
      } else {
        return {
          status: RESPONSES.NOTFOUND,
          error: true,
          message: 'Representative not found',
        };
      }
    } catch (error: any) {
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * @param {string} representativeId
   * @param {boolean} isDeleted
   * @returns {Promise<PromiseResolve>}
   * @memberof RepresentativeService
   */
  async blockUnblockRepresentative(representativeId: string, isDeleted: boolean): Promise<PromiseResolve> {
    try {
      const objectId = new Types.ObjectId(representativeId);

      const updatedRepresentative = await representativeSchemaModel.findOneAndUpdate({ _id: objectId, userType: 'representative' }, { isDeleted }, { new: true }).select('-password').lean();

      if (updatedRepresentative) {
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: `Representative ${isDeleted ? 'blocked' : 'unblocked'} successfully`,
          data: updatedRepresentative,
        };
      } else {
        return {
          status: RESPONSES.NOTFOUND,
          error: true,
          message: 'Representative not found',
        };
      }
    } catch (error: any) {
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * @param {string} representativeId
   * @returns {Promise<PromiseResolve>}
   * @memberof RepresentativeService
   */
  async deleteRepresentative(representativeId: string): Promise<PromiseResolve> {
    try {
      const objectId = new Types.ObjectId(representativeId);

      const deletedRepresentative = await representativeSchemaModel
        .findOneAndUpdate({ _id: objectId, userType: 'representative' }, { isActive: false, isDeleted: true }, { new: true })
        .select('-password')
        .lean();

      if (deletedRepresentative) {
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: 'Representative deleted successfully',
          data: deletedRepresentative,
        };
      } else {
        return {
          status: RESPONSES.NOTFOUND,
          error: true,
          message: 'Representative not found',
        };
      }
    } catch (error: any) {
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * @param {FilterQuery<IRepresentative>} searchDetails
   * @param {string[]} projection
   * @returns {Promise<PromiseResolve>}
   * @memberof RepresentativeService
   */
  async fetchRepresentativeListCsv(searchDetails: FilterQuery<IRepresentative>, projection?: string[]): Promise<PromiseResolve> {
    try {
      const projectionString = projection ? projection.join(' ') : '-password';

      // Add userType filter to ensure we only get representatives
      const query = {
        ...searchDetails,
        userType: 'representative',
      };

      const representatives = await representativeSchemaModel.find(query).select(projectionString).lean().exec();

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: 'Representatives data for CSV export',
        data: representatives,
      };
    } catch (error: any) {
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }
}

export default new RepresentativeService();
