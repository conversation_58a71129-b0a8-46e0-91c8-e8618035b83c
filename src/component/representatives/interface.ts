import { FilterQuery } from 'mongoose';
import { IPagination, PromiseResolve } from '../../utils/common.interface';
import { IRepresentative, IUpdateRepresentative, ICreateRepresentative } from './representative.model';

export interface IRepresentativeService {
  /**
   * @param {ICreateRepresentative} representativeData
   * @returns {Promise<PromiseResolve>}
   * @memberof RepresentativeService
   */
  createRepresentative(representativeData: ICreateRepresentative): Promise<PromiseResolve>;

  /**
   * @param {FilterQuery<IRepresentative>} searchDetails
   * @param {string[]} fields
   * @param {string[]} excludeFields
   * @returns {Promise<PromiseResolve>}
   * @memberof RepresentativeService
   */
  fetchRepresentativeDetails(searchDetails: FilterQuery<IRepresentative>, fields?: string[], excludeFields?: string[]): Promise<PromiseResolve>;

  /**
   * @param {FilterQuery<IRepresentative>} searchDetails
   * @param {string[]} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof RepresentativeService
   */
  fetchRepresentativeList(searchDetails: FilterQuery<IRepresentative>, projection?: string[], pagination?: IPagination): Promise<PromiseResolve>;

  /**
   * @param {string} representativeId
   * @param {IUpdateRepresentative} updateData
   * @returns {Promise<PromiseResolve>}
   * @memberof RepresentativeService
   */
  updateRepresentative(representativeId: string, updateData: IUpdateRepresentative): Promise<PromiseResolve>;

  /**
   * @param {string} representativeId
   * @param {boolean} isBlocked
   * @returns {Promise<PromiseResolve>}
   * @memberof RepresentativeService
   */
  blockUnblockRepresentative(representativeId: string, isBlocked: boolean): Promise<PromiseResolve>;

  /**
   * @param {string} representativeId
   * @returns {Promise<PromiseResolve>}
   * @memberof RepresentativeService
   */
  deleteRepresentative(representativeId: string): Promise<PromiseResolve>;

  /**
   * @param {FilterQuery<IRepresentative>} searchDetails
   * @param {string[]} projection
   * @returns {Promise<PromiseResolve>}
   * @memberof RepresentativeService
   */
  fetchRepresentativeListCsv(searchDetails: FilterQuery<IRepresentative>, projection?: string[]): Promise<PromiseResolve>;
}

export interface IRepresentativeFilters {
  name?: string;
  email?: string;
  kycStatus?: string;
  isActive?: boolean;
  isBlocked?: boolean;
  smartWalletAddress?: string;
}
