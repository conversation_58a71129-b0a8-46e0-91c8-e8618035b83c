// Re-export user model interfaces and schema for representatives
// Representatives are users with userType = 'representative'
export { IUserModel as IRepresentative, IUpdateUserModel as IUpdateRepresentative, userSchema as representativeSchemaModel } from '../userAuthentications/user.model';
import { UserType, KycStatus } from '../../utils/common.interface';

export interface ICreateRepresentative {
  name: string;
  email: string;
  password?: string;
  walletAddress?: string;
}

// Helper type for representative-specific filters
export interface IRepresentativeFilters {
  name?: string;
  email?: string;
  kycStatus?: KycStatus;
  isActive?: boolean;
  isDeleted?: boolean;
  walletAddress?: string;
  userType?: UserType.Representative;
}
