import mongoose, { Schema, Document, Types } from 'mongoose';
import { RES_MSG } from '../../utils/responseUtils';

export interface IRepresentative extends Document {
  name: string;
  email: string;
  password?: string;
  kycStatus: string;
  smartWalletAddress?: string;
  isActive: boolean;
  isBlocked: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IUpdateRepresentative {
  name?: string;
  email?: string;
  password?: string;
  kycStatus?: string;
  smartWalletAddress?: string;
  isActive?: boolean;
  isBlocked?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ICreateRepresentative {
  name: string;
  email: string;
  password?: string;
}

const representativeSchema: Schema<IRepresentative> = new Schema(
  {
    name: { 
      type: String, 
      required: true,
      trim: true
    },
    email: { 
      type: String, 
      required: true, 
      unique: true,
      lowercase: true,
      trim: true
    },
    password: {
      type: String,
      required: false,
    },
    kycStatus: {
      type: String,
      enum: ['Approved', 'Pending', 'Rejected', 'Not Started'],
      default: 'Pending',
    },
    smartWalletAddress: {
      type: String,
      required: false,
      default: null,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isBlocked: {
      type: Boolean,
      default: false,
    },
  },
  {
    versionKey: false,
    timestamps: true,
  },
);

// Create indexes for better query performance
representativeSchema.index({ email: 1 });
representativeSchema.index({ isActive: 1 });
representativeSchema.index({ kycStatus: 1 });

const representativeSchemaModel = mongoose.model<IRepresentative>('Representative', representativeSchema);

export { representativeSchemaModel };
