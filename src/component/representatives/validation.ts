import * as <PERSON><PERSON> from 'joi';
import { JoiValidationResult } from '../../utils/common.interface';
import { RESPONSES } from '../../utils/responseUtils';

const RepresentativeValidation = {
  /**
   * @param {any} body
   * @returns {JoiValidationResult}
   * @memberof RepresentativeValidation
   */
  createRepresentativeValidation(body: any): JoiValidationResult {
    const schema = Joi.object({
      name: Joi.string().min(2).max(100).required().trim().messages({
        'string.empty': 'Name is required',
        'string.min': 'Name must be at least 2 characters long',
        'string.max': 'Name cannot exceed 100 characters',
        'any.required': 'Name is required',
      }),
      email: Joi.string().email().required().lowercase().trim().messages({
        'string.empty': 'Email is required',
        'string.email': 'Please provide a valid email address',
        'any.required': 'Email is required',
      }),
      password: Joi.string().min(8).max(128).optional().pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]')).messages({
        'string.min': 'Password must be at least 8 characters long',
        'string.max': 'Password cannot exceed 128 characters',
        'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
      }),
      walletAddress: Joi.string().optional().allow(null, '').messages({
        'string.base': 'Wallet address must be a string',
      }),
    });

    const { error, value } = schema.validate(body);
    if (error) {
      return {
        error: true,
        message: error.details[0].message,
        value: body,
        status: RESPONSES.BAD_REQUEST,
      };
    }
    return {
      error: false,
      value,
    };
  },

  /**
   * @param {any} body
   * @returns {JoiValidationResult}
   * @memberof RepresentativeValidation
   */
  updateRepresentativeValidation(body: any): JoiValidationResult {
    const schema = Joi.object({
      name: Joi.string().min(2).max(100).optional().trim().messages({
        'string.min': 'Name must be at least 2 characters long',
        'string.max': 'Name cannot exceed 100 characters',
      }),
      email: Joi.string().email().optional().lowercase().trim().messages({
        'string.email': 'Please provide a valid email address',
      }),
      password: Joi.string().min(8).max(128).optional().pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]')).messages({
        'string.min': 'Password must be at least 8 characters long',
        'string.max': 'Password cannot exceed 128 characters',
        'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
      }),
      kycStatus: Joi.string().valid('NOT_STARTED', 'PENDING', 'APPROVED', 'DECLINED', 'HOLD', 'PRECHECKRED').optional().messages({
        'any.only': 'KYC Status must be one of: NOT_STARTED, PENDING, APPROVED, DECLINED, HOLD, PRECHECKRED',
      }),
      walletAddress: Joi.string().optional().allow(null, '').messages({
        'string.base': 'Wallet address must be a string',
      }),
      isActive: Joi.boolean().optional().messages({
        'boolean.base': 'isActive must be a boolean value',
      }),
      isDeleted: Joi.boolean().optional().messages({
        'boolean.base': 'isDeleted must be a boolean value',
      }),
    });

    const { error, value } = schema.validate(body);
    if (error) {
      return {
        error: true,
        message: error.details[0].message,
        value: body,
        status: RESPONSES.BAD_REQUEST,
      };
    }
    return {
      error: false,
      value,
    };
  },

  /**
   * @param {any} body
   * @returns {JoiValidationResult}
   * @memberof RepresentativeValidation
   */
  blockUnblockRepresentativeValidation(body: any): JoiValidationResult {
    const schema = Joi.object({
      representativeId: Joi.string()
        .required()
        .pattern(/^[0-9a-fA-F]{24}$/)
        .messages({
          'string.empty': 'Representative ID is required',
          'string.pattern.base': 'Invalid representative ID format',
          'any.required': 'Representative ID is required',
        }),
      isDeleted: Joi.boolean().required().messages({
        'boolean.base': 'isDeleted must be a boolean value',
        'any.required': 'isDeleted status is required',
      }),
    });

    const { error, value } = schema.validate(body);
    if (error) {
      return {
        error: true,
        message: error.details[0].message,
        value: body,
        status: RESPONSES.BAD_REQUEST,
      };
    }
    return {
      error: false,
      value,
    };
  },

  /**
   * @param {any} query
   * @returns {JoiValidationResult}
   * @memberof RepresentativeValidation
   */
  getRepresentativeListValidation(query: any): JoiValidationResult {
    const schema = Joi.object({
      page: Joi.number().integer().min(1).optional().default(1).messages({
        'number.base': 'Page must be a number',
        'number.integer': 'Page must be an integer',
        'number.min': 'Page must be at least 1',
      }),
      limit: Joi.number().integer().min(1).max(100).optional().default(10).messages({
        'number.base': 'Limit must be a number',
        'number.integer': 'Limit must be an integer',
        'number.min': 'Limit must be at least 1',
        'number.max': 'Limit cannot exceed 100',
      }),
      search: Joi.string().optional().allow('').trim().messages({
        'string.base': 'Search must be a string',
      }),
      kycStatus: Joi.string().valid('NOT_STARTED', 'PENDING', 'APPROVED', 'DECLINED', 'HOLD', 'PRECHECKRED').optional().messages({
        'any.only': 'KYC Status must be one of: NOT_STARTED, PENDING, APPROVED, DECLINED, HOLD, PRECHECKRED',
      }),
      isActive: Joi.boolean().optional().messages({
        'boolean.base': 'isActive must be a boolean value',
      }),
      isDeleted: Joi.boolean().optional().messages({
        'boolean.base': 'isDeleted must be a boolean value',
      }),
      sortBy: Joi.string().valid('name', 'email', 'kycStatus', 'createdAt', 'updatedAt').optional().default('createdAt').messages({
        'any.only': 'sortBy must be one of: name, email, kycStatus, createdAt, updatedAt',
      }),
      sortOrder: Joi.string().valid('asc', 'desc').optional().default('desc').messages({
        'any.only': 'sortOrder must be either asc or desc',
      }),
    });

    const { error, value } = schema.validate(query);
    if (error) {
      return {
        error: true,
        message: error.details[0].message,
        value: query,
        status: RESPONSES.BAD_REQUEST,
      };
    }
    return {
      error: false,
      value,
    };
  },

  /**
   * @param {any} params
   * @returns {JoiValidationResult}
   * @memberof RepresentativeValidation
   */
  representativeIdValidation(params: any): JoiValidationResult {
    const schema = Joi.object({
      id: Joi.string()
        .required()
        .pattern(/^[0-9a-fA-F]{24}$/)
        .messages({
          'string.empty': 'Representative ID is required',
          'string.pattern.base': 'Invalid representative ID format',
          'any.required': 'Representative ID is required',
        }),
    });

    const { error, value } = schema.validate(params);
    if (error) {
      return {
        error: true,
        message: error.details[0].message,
        value: params,
        status: RESPONSES.BAD_REQUEST,
      };
    }
    return {
      error: false,
      value,
    };
  },
};

export default RepresentativeValidation;
