import { Request, Response } from 'express';
import { PromiseResolve } from '../../utils/common.interface';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import { ResponseHandler } from '../../helpers/response.helper';
import logger from '../../helpers/logging/logger.helper';
import representativeService from './service';
import { ICreateRepresentative, IUpdateRepresentative } from './representative.model';

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<PromiseResolve>}
 */
export async function createRepresentativeController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const representativeData: ICreateRepresentative = req.body;

    const result = await representativeService.createRepresentative(representativeData);

    if (result.error) {
      return ResponseHandler.error(res, {
        status: result.status,
        error: true,
        message: result.message,
      });
    }

    return ResponseHandler.success(res, {
      status: result.status,
      error: false,
      message: result.message,
      data: result.data,
    });
  } catch (error: any) {
    logger.error(error, 'createRepresentativeController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<PromiseResolve>}
 */
export async function getRepresentativeListController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { page, limit, search, kycStatus, isActive, isDeleted, sortBy, sortOrder } = req.query;

    // Build search filters
    const searchDetails: any = {};

    if (kycStatus) {
      searchDetails.kycStatus = kycStatus;
    }

    if (isActive !== undefined) {
      searchDetails.isActive = isActive === 'true';
    }

    if (isDeleted !== undefined) {
      searchDetails.isDeleted = isDeleted === 'true';
    }

    // Build sort object
    const sort: any = {};
    if (sortBy) {
      sort[sortBy as string] = sortOrder === 'asc' ? 1 : -1;
    } else {
      sort.createdAt = -1;
    }

    const pagination = {
      page: parseInt(page as string) || 1,
      limit: parseInt(limit as string) || 10,
      sort,
      search: search as string,
    };

    const result = await representativeService.fetchRepresentativeList(searchDetails, undefined, pagination);

    if (result.error) {
      return ResponseHandler.error(res, {
        status: result.status,
        error: true,
        message: result.message,
      });
    }

    return ResponseHandler.success(res, {
      status: result.status,
      error: false,
      message: result.message,
      data: result.data,
    });
  } catch (error: any) {
    logger.error(error, 'getRepresentativeListController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<PromiseResolve>}
 */
export async function getRepresentativeController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { id } = req.params;

    const result = await representativeService.fetchRepresentativeDetails(
      { _id: id },
      undefined,
      ['password'], // Exclude password field
    );

    if (result.error) {
      return ResponseHandler.error(res, {
        status: result.status,
        error: true,
        message: result.message,
      });
    }

    return ResponseHandler.success(res, {
      status: result.status,
      error: false,
      message: result.message,
      data: result.data,
    });
  } catch (error: any) {
    logger.error(error, 'getRepresentativeController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<PromiseResolve>}
 */
export async function updateRepresentativeController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { id } = req.params;
    const updateData: IUpdateRepresentative = req.body;

    const result = await representativeService.updateRepresentative(id, updateData);

    if (result.error) {
      return ResponseHandler.error(res, {
        status: result.status,
        error: true,
        message: result.message,
      });
    }

    return ResponseHandler.success(res, {
      status: result.status,
      error: false,
      message: result.message,
      data: result.data,
    });
  } catch (error: any) {
    logger.error(error, 'updateRepresentativeController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<PromiseResolve>}
 */
export async function blockUnblockRepresentativeController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { representativeId, isDeleted } = req.body;

    const result = await representativeService.blockUnblockRepresentative(representativeId, isDeleted);

    if (result.error) {
      return ResponseHandler.error(res, {
        status: result.status,
        error: true,
        message: result.message,
      });
    }

    return ResponseHandler.success(res, {
      status: result.status,
      error: false,
      message: result.message,
      data: result.data,
    });
  } catch (error: any) {
    logger.error(error, 'blockUnblockRepresentativeController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<PromiseResolve>}
 */
export async function deleteRepresentativeController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { id } = req.params;

    const result = await representativeService.deleteRepresentative(id);

    if (result.error) {
      return ResponseHandler.error(res, {
        status: result.status,
        error: true,
        message: result.message,
      });
    }

    return ResponseHandler.success(res, {
      status: result.status,
      error: false,
      message: result.message,
      data: result.data,
    });
  } catch (error: any) {
    logger.error(error, 'deleteRepresentativeController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<PromiseResolve>}
 */
export async function getRepresentativeListCsvController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { kycStatus, isActive, isDeleted } = req.query;

    // Build search filters
    const searchDetails: any = {};

    if (kycStatus) {
      searchDetails.kycStatus = kycStatus;
    }

    if (isActive !== undefined) {
      searchDetails.isActive = isActive === 'true';
    }

    if (isDeleted !== undefined) {
      searchDetails.isDeleted = isDeleted === 'true';
    }

    const result = await representativeService.fetchRepresentativeListCsv(searchDetails);

    if (result.error) {
      return ResponseHandler.error(res, {
        status: result.status,
        error: true,
        message: result.message,
      });
    }

    return ResponseHandler.success(res, {
      status: result.status,
      error: false,
      message: result.message,
      data: result.data,
    });
  } catch (error: any) {
    logger.error(error, 'getRepresentativeListCsvController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}
