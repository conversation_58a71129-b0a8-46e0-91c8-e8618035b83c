# Representatives Management API

This module provides comprehensive API endpoints for managing representatives in the Valuit tokenization platform.

**Note:** Representatives are stored in the existing `users` collection with `userType` set to `'representative'`. This module uses the existing user schema instead of creating a separate collection.

## Features

- Create new representatives (using existing user schema)
- List representatives with pagination and filtering
- Get individual representative details
- Update representative information
- Block/unblock representatives (using isDeleted flag)
- Soft delete representatives
- Export representatives data to CSV

## API Endpoints

### Base URL

All endpoints are prefixed with `/v1/auth/representatives`

### Authentication

All endpoints require authentication and admin permissions.

### Endpoints

#### 1. Create Representative

**POST** `/v1/auth/representatives`

Creates a new representative in the system.

**Request Body:**

```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "SecurePass123!", // Optional
  "walletAddress": "0x1234567890abcdef..." // Optional
}
```

**Response:**

```json
{
  "message": "Representative created successfully",
  "status": 200,
  "error": false,
  "data": {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "name": "<PERSON> <PERSON>e",
    "email": "<EMAIL>",
    "kycStatus": "Pending",
    "isActive": true,
    "isBlocked": false,
    "createdAt": "2023-09-06T10:30:00.000Z",
    "updatedAt": "2023-09-06T10:30:00.000Z"
  }
}
```

#### 2. Get Representatives List

**GET** `/v1/auth/representatives`

Retrieves a paginated list of representatives with optional filtering.

**Query Parameters:**

- `page` (number, optional): Page number (default: 1)
- `limit` (number, optional): Items per page (default: 10, max: 100)
- `search` (string, optional): Search by name or email
- `kycStatus` (string, optional): Filter by KYC status (Approved, Pending, Rejected, Not Started)
- `isActive` (boolean, optional): Filter by active status
- `isBlocked` (boolean, optional): Filter by blocked status
- `sortBy` (string, optional): Sort field (name, email, kycStatus, createdAt, updatedAt)
- `sortOrder` (string, optional): Sort order (asc, desc)

**Example Request:**

```
GET /v1/auth/representatives?page=1&limit=10&kycStatus=Approved&sortBy=createdAt&sortOrder=desc
```

**Response:**

```json
{
  "message": "Representatives fetched successfully",
  "status": 200,
  "error": false,
  "data": {
    "representatives": [...],
    "currentPage": 1,
    "totalPages": 5,
    "totalCount": 50,
    "nextPage": 2,
    "previousPage": null
  }
}
```

#### 3. Get Representative by ID

**GET** `/v1/auth/representatives/:id`

Retrieves details of a specific representative.

**Response:**

```json
{
  "message": "Data success",
  "status": 200,
  "error": false,
  "data": {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "name": "John Doe",
    "email": "<EMAIL>",
    "kycStatus": "Approved",
    "smartWalletAddress": "0x1234567890abcdef...",
    "isActive": true,
    "isBlocked": false,
    "createdAt": "2023-09-06T10:30:00.000Z",
    "updatedAt": "2023-09-06T12:45:00.000Z"
  }
}
```

#### 4. Update Representative

**PUT** `/v1/auth/representatives/:id`

Updates representative information.

**Request Body:**

```json
{
  "name": "John Smith",
  "kycStatus": "Approved",
  "smartWalletAddress": "0x1234567890abcdef..."
}
```

#### 5. Block/Unblock Representative

**POST** `/v1/auth/representatives/block-unblock`

Blocks or unblocks a representative.

**Request Body:**

```json
{
  "representativeId": "64f8a1b2c3d4e5f6a7b8c9d0",
  "isBlocked": true
}
```

#### 6. Delete Representative

**DELETE** `/v1/auth/representatives/:id`

Soft deletes a representative (sets isActive to false).

#### 7. Export to CSV

**GET** `/v1/auth/representatives/csv`

Exports representatives data for CSV download. Supports same filtering as the list endpoint.

## Data Model

### Representative Schema

Representatives use the existing User schema with `userType` set to `'representative'`:

```typescript
interface IRepresentative extends IUserModel {
  userType: 'representative';
  name: string;
  email: string;
  password?: string;
  kycStatus: KycStatus; // 'NOT_STARTED' | 'PENDING' | 'APPROVED' | 'DECLINED' | 'HOLD' | 'PRECHECKRED'
  walletAddress?: string;
  isActive: boolean;
  isDeleted: boolean; // Used for blocking/unblocking
  createdAt?: Date;
  updatedAt?: Date;
  // ... other user schema fields
}
```

## Validation Rules

### Create Representative

- `name`: Required, 2-100 characters
- `email`: Required, valid email format
- `password`: Optional, min 8 chars, must contain uppercase, lowercase, number, and special character

### Update Representative

- All fields optional
- Same validation rules as create when provided

### Block/Unblock

- `representativeId`: Required, valid MongoDB ObjectId
- `isDeleted`: Required boolean (true to block, false to unblock)

## Error Responses

All endpoints return standardized error responses:

```json
{
  "message": "Error description",
  "status": 400,
  "error": true,
  "data": null
}
```

Common HTTP status codes:

- `400`: Bad Request (validation errors)
- `401`: Unauthorized
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found
- `409`: Conflict (duplicate email)
- `500`: Internal Server Error

## Security Features

- Password hashing using bcrypt
- Email uniqueness validation
- Admin permission checks
- Input validation and sanitization
- SQL injection prevention through Mongoose ODM
