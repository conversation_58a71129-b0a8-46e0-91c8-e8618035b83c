import { Request, Response } from 'express';
import { PromiseResolve, queueMessageTypeEnum } from '../../utils/common.interface';
import transferService from './service';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import CustomError from '../../helpers/customError.helper';
import { ResponseHandler } from '../../helpers/response.helper';
import logger from '../../helpers/logging/logger.helper';
import mongoose from 'mongoose';
import UserService from '../userAuthentications/service';
import emailHelper from '../../helpers/email.helper';
import { transferAgentSchema } from './transferagent.model';
import kafkaService from '../../services/kafkaService';
import { userSchema } from '../userAuthentications/user.model';
import redisHelper from '../../helpers/redis.helper';
import CommonHelper from '../../helpers/common.helper';

const taBaseUrl = process.env.TAURL;

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise < PromiseResolve >}
 */

export async function createTransferAgent(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    let { name, email, walletAddress } = req.body;
    name = name.toLowerCase();
    email = email.toLowerCase();
    walletAddress = walletAddress.toLowerCase();
    // Check if wallet already exists
    const userWalletExists = await userSchema.findOne({
      wallets: { $elemMatch: { address: walletAddress } },
    });

    if (userWalletExists) {
      throw new CustomError(RES_MSG.USER.WALLET_ALREADY_EXIST, RESPONSES.CONFLICT);
    }

    const [emailExists, userExists, walletAddressExists, userWalletAddressExists] = await Promise.all([
      transferService.fetchOfferingDetails({ email, walletAddress }),
      UserService.fetchUserDetails({ email }),
      transferService.fetchOfferingDetails({ walletAddress }),
      UserService.searchUsersAndAgents(walletAddress),
    ]);
    if (!emailExists.error) {
      throw new CustomError(RES_MSG.USER.TA_ALREADY_EXIST, RESPONSES.CONFLICT);
    }
    if (!userExists.error) {
      throw new CustomError(RES_MSG.USER.USER_ALREADY_EXIST, RESPONSES.CONFLICT);
    }
    if (!walletAddressExists.error || userWalletAddressExists.error) {
      throw new CustomError(RES_MSG.USER.WALLET_ALREADY_EXIST, RESPONSES.CONFLICT);
    }

    // Create user
    const createUserResp = await transferService.createOffering({ ...req.body, email: email });
    if (createUserResp.error) {
      throw new CustomError(createUserResp.message || RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.CONFLICT);
    }

    const user = createUserResp?.data;

    // Send Kafka message
    await kafkaService.sendMessageToTransferagnet({
      value: JSON.stringify({ ...user, type: queueMessageTypeEnum.USER }),
    });

    // Capitalize first letter
    const capitalizeFirstLetter = (str: string): string => str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();

    const emailDetail = {
      name: capitalizeFirstLetter(name),
      email,
      password: user?.password,
      walletAddress,
      taBaseUrl,
    };

    // Send email
    emailHelper.sendEmailTemplate(email, 'transferAgent', emailDetail);

    return ResponseHandler.success(res, {
      status: createUserResp.status || RESPONSES.SUCCESS,
      error: false,
      message: createUserResp.message || RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
      data: '',
    });
  } catch (error: any) {
    logger.error(error, 'createTransferAgent Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function getTransferAgentListController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { page = 1, limit = 10, sort = JSON.stringify({ createdAt: -1 }), search = '', status } = req.query;

    const disallowedSymbolsRegex = /[!+#$%^*(),?":{}|<>]/;
    const validateSearchResult = await CommonHelper.validateStringRegex(search as string, disallowedSymbolsRegex, RES_MSG.ERROR_MSG.DISALLOWED_SYMBOLS);

    // If validation fails, return the error response immediately
    if (validateSearchResult.error) {
      return ResponseHandler.error(res, {
        status: validateSearchResult.status,
        error: true,
        message: validateSearchResult.message,
      });
    }
    let isActive;
    if (status) {
      const statusUpper = (status as string).toUpperCase();
      if (statusUpper === 'BLOCKED') {
        isActive = false;
      } else if (statusUpper === 'UNBLOCKED') {
        isActive = true;
      }
    }

    const filters = {
      ...(isActive !== undefined && { isActive }),
    };

    let sortCriteria = JSON.parse(sort as string);
    const projection = ['name', 'email', 'isActive', 'noOfOfferings', 'offeringId', 'status', 'walletAddress', 'createdAt'];
    const { data } = await transferService.fetchTransferAgentList(filters, projection, {
      page: Number(page),
      limit: Number(limit),
      sort: sortCriteria,
      search,
    });

    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.USER.USERS_FETCH,
      data: data,
    });
  } catch (error) {
    logger.error(error, 'getTransferAgentListController Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function getTransferAgentController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { userId }: any = req.query;
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return ResponseHandler.error(res, {
        message: 'invalid id',
        status: RESPONSES.BAD_REQUEST,
        error: true,
      });
    }

    const result: PromiseResolve = await transferService.fetchTaDetails(userId);
    if (!result) {
      return ResponseHandler.error(res, {
        message: RES_MSG.ERROR_MSG.USER_NOT_FOUND,
        status: 400,
        error: true,
      });
    }

    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
      data: result?.data,
    });
  } catch (error) {
    logger.error(error, 'getUserslist Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function transferAgentListCsv(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { sort = JSON.stringify({ createdAt: -1 }), startDate = '', endDate = '' } = req.query;

    // Initialize filters object
    const filters: any = {};

    // Add date filtering based on startDate and endDate
    if (startDate && endDate) {
      filters.createdAt = {
        $gte: new Date(startDate as string),
        $lte: new Date(endDate as string),
      };
    } else if (startDate) {
      filters.createdAt = { $gte: new Date(startDate as string) };
    } else if (endDate) {
      filters.createdAt = { $lte: new Date(endDate as string) };
    }

    // Parse sort criteria
    let sortCriteria = JSON.parse(sort as string);

    // Define the projection fields
    const projection = ['name', 'email', 'isActive', 'noOfOfferings', 'offeringId', 'status', 'walletAddress', 'createdAt'];

    // Fetch user list with the applied filters and sorting
    const userDetails = await transferService.fetchUserListcsv(filters, projection);

    return ResponseHandler.success(res, {
      status: 200,
      error: false,
      message: RES_MSG.USER.USERS_FETCH,
      data: userDetails.data,
    });
  } catch (error) {
    logger.error(error, 'getUserslist Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise <PromiseResolve>}
 */
export async function unblock(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { email, isActive } = req.body;
    // Fetch user details and return specific fields
    const userDetails = await transferService.fetchOfferingDetails({ email }, ['name', 'email', 'isActive']);
    if (userDetails.error) {
      throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.BAD_REQUEST);
    }

    const currentIsActive = userDetails.data.isActive;
    if (currentIsActive === isActive) {
      throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.BAD_REQUEST);
    }
    // gRPC call to unblock the user
    // Update user's active status and reset kycCount if activated
    const updatedUser = await transferAgentSchema.findOneAndUpdate(
      { email },
      {
        isActive,
      },
      { new: true }, // Return the updated document
    );
    const emailDetails = { name: userDetails.data.name };
    let message;
    if (isActive === true) {
      emailHelper.sendEmailTemplate(email, 'accountUnBlock', emailDetails);
      message = RES_MSG.COMMON.ADMIN_UNBLOCK_TRANSFERAGENT;
    } else {
      emailHelper.sendEmailTemplate(email, 'accountBlocked', emailDetails);
      redisHelper.deleteKey(`accessToken:${email}`, 1);
      message = RES_MSG.COMMON.ADMIN_BLOCK_TRANSFERAGENT;
    }

    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message,
      data: isActive,
    });
  } catch (error: any) {
    logger.error(error, 'unblock Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}
