import { FilterQuery } from 'mongoose';
import { IPagination, PromiseResolve } from '../../utils/common.interface';
import { ITransferAgent, IUpdateTransferAgent } from './transferagent.model';

export interface ITransferAgentService {
  /**
   * @param {IOffering} searchDetails
   * @param {IOffering} fields
   * @param {IOffering} excludeFields
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */

  fetchOfferingDetails(searchDetails: FilterQuery<ITransferAgent>, fields?: string[], excludeFields?: string[], isKyc?: Boolean): Promise<PromiseResolve>;

  /**
   * @param {IOffering} searchDetails
   * @param {IOffering} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  fetchTransferAgentList(searchDetails: FilterQuery<IUpdateTransferAgent>, projection?: string[], pagination?: IPagination): Promise<PromiseResolve>;

  /**
   * @param {IUpdateOffering} body
   * @param {IOffering} filter
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  updateOfferingDetails(body: IUpdateTransferAgent, filter: FilterQuery<ITransferAgent>): Promise<PromiseResolve>;

  /**
   * @param {IUpdateOffering} body
   * @param {IOffering} filter
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  createOffering(body: IUpdateTransferAgent): Promise<PromiseResolve>;

  /**
   * @param {IOffering} searchDetails
   * @param {Array} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */

  fetchUserListcsv(searchDetails: FilterQuery<IUpdateTransferAgent>, projection?: string[]): Promise<PromiseResolve>;

  /**
   * @param {IOffering} searchDetails
   * @param {Array} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  fetchOfferingList(searchDetails: any, projection?: string[], pagination?: IPagination): Promise<PromiseResolve>;

  /**
   * @param {string} taId
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  fetchTaDetails(taId: string): Promise<PromiseResolve>;
}
