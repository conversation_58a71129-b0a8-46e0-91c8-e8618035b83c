import { userSchema } from '../component/userAuthentications/user.model';
import * as bcrypt from 'bcrypt';
import emailHelper from '../helpers/email.helper';
import { UserType } from '../utils/common.interface';
const mongoose = require('mongoose');
const MONGODB_URI = 'mongodb://localhost:27017/admin';
const seed = process.env.SEED;
const walletAddress = process.env.ADMINADDRESS;

const users = {
  name: 'Admin',
  email: process.env.EMAIL,
  countryCode: '+91',
  mobile: process.env.MOBILE,
  userType: UserType.Admin,
  isEmailVerify: true,
  isOtpActive: false,
  walletAddress: walletAddress,
};
// Helper function to generate a random password
export function generateRandomPassword(length: number) {
  console.log('generateRandomPassword', seed);
  const chars = seed;
  let password = '';
  for (let i = 0; i < length; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}

async function seedUsers() {
  try {
    // Check for a specific user
    const user = await userSchema.findOne({ email: users.email }).exec();
    if (!user) {
      // Generate a random password
      const randomPassword = generateRandomPassword(12);
      const hashedPassword = await bcrypt.hash(randomPassword, 10);
      // Add the password to the user data
      const newUser = {
        ...users,
        password: hashedPassword,
      };
      // Create the user
      const createdUser = await userSchema.create(newUser);
      const emailDetail = {
        name: process.env.NAME,
        password: randomPassword,
      };
      emailHelper.sendEmailTemplate(users.email, 'Adminemail', emailDetail);
      // Send the email with the random password
      // await sendPasswordEmail(users.email, randomPassword);
    } else {
    }
  } catch (err) {
    console.error('Error seeding user data:', err);
  }
}

// Export the seedUsers function
module.exports = seedUsers;
