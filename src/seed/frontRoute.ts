import { frontendroutesSchema } from '../component/subadmin/frontend_routes.model';
import mongoose from 'mongoose';

// Data to be migrated
const routeData = [
  { _id: new mongoose.Types.ObjectId('6731a081999334e0230cd9da'), moduelsId: '672c70c205e65803246ffc43', routePath: '/v1/auth/getUserslist' },
  { _id: new mongoose.Types.ObjectId('6731f31d2cfb7d661da8e75a'), moduelsId: '672c70c205e65803246ffc43', routePath: '/v1/auth/getUsersListCsv' },
  { _id: new mongoose.Types.ObjectId('6731f3272cfb7d661da8e75b'), moduelsId: '672c70c205e65803246ffc43', routePath: '/v1/auth/approve' },
  { _id: new mongoose.Types.ObjectId('6731f33d2cfb7d661da8e75c'), moduelsId: '672c70c205e65803246ffc43', routePath: '/v1/auth/unblock' },
  { _id: new mongoose.Types.ObjectId('6731f34c2cfb7d661da8e75d'), moduelsId: '672c70c205e65803246ffc43', routePath: '/v1/auth/getUserdata' },
  { _id: new mongoose.Types.ObjectId('6731f3a72cfb7d661da8e75f'), moduelsId: '672c705305e65803246ffc40', routePath: '/v1/auth/getIssuerListCsv' },
  { _id: new mongoose.Types.ObjectId('6731f3bc2cfb7d661da8e760'), moduelsId: '672c705305e65803246ffc40', routePath: '/v1/auth/getissuer' },
  { _id: new mongoose.Types.ObjectId('6731f43a2cfb7d661da8e761'), moduelsId: '672c70e305e65803246ffc45', routePath: '/v1/auth/createTransferAgent' },
  { _id: new mongoose.Types.ObjectId('6731f4462cfb7d661da8e762'), moduelsId: '672c70e305e65803246ffc45', routePath: '/v1/auth/transferAgentList' },
  { _id: new mongoose.Types.ObjectId('6731f4502cfb7d661da8e763'), moduelsId: '672c70e305e65803246ffc45', routePath: '/v1/auth/transferAgent/:userId' },
  { _id: new mongoose.Types.ObjectId('6731f45a2cfb7d661da8e764'), moduelsId: '672c70e305e65803246ffc45', routePath: '/v1/auth/changeStatus' },
  { _id: new mongoose.Types.ObjectId('6731f4702cfb7d661da8e765'), moduelsId: '672c70e305e65803246ffc45', routePath: '/v1/auth/transferAgentListCsv' },
  { _id: new mongoose.Types.ObjectId('6731f4982cfb7d661da8e768'), moduelsId: '672c70af05e65803246ffc42', routePath: '/v1/auth/getManageIssuerListCsv' },
  { _id: new mongoose.Types.ObjectId('6731f4ff2cfb7d661da8e76d'), moduelsId: '672c70af05e65803246ffc42', routePath: '/v1/auth/offering/manage-issuers' },
  { _id: new mongoose.Types.ObjectId('6731f5082cfb7d661da8e76e'), moduelsId: '672c70ce05e65803246ffc44', routePath: '/v1/auth/offering' },
  { _id: new mongoose.Types.ObjectId('6731f5082cfb7d661da8e76e'), moduelsId: '672c70ce05e65803246ffc44', routePath: '/v1/auth/offeringList' },
  { _id: new mongoose.Types.ObjectId('6731f51c2cfb7d661da8e76f'), moduelsId: '672c70af05e65803246ffc44', routePath: '/v1/auth/offering/:id' },
  { _id: new mongoose.Types.ObjectId('6731f52c2cfb7d661da8e770'), moduelsId: '672c70af05e65803246ffc42', routePath: '/v1/auth/rejectOffering' },
  { _id: new mongoose.Types.ObjectId('6731f5c12cfb7d661da8e774'), moduelsId: '672c5bed05e65803246ffc2f', routePath: '/v1/auth/dashboard' },
  { _id: new mongoose.Types.ObjectId('6731f5ce2cfb7d661da8e775'), moduelsId: '672c5bed05e65803246ffc2f', routePath: '/v1/auth/user' },
  { _id: new mongoose.Types.ObjectId('67331785a25b6e3e5e2bb40d'), moduelsId: '672c5c4a05e65803246ffc33', routePath: '/v1/auth/createSubadmin' },
  { _id: new mongoose.Types.ObjectId('6733178fa25b6e3e5e2bb40e'), moduelsId: '672c5c4a05e65803246ffc33', routePath: '/v1/auth/loggedInUserModuleList' },
  { _id: new mongoose.Types.ObjectId('6733179ca25b6e3e5e2bb40f'), moduelsId: '672c5c4a05e65803246ffc33', routePath: '/v1/auth/getModuleList' },
  { _id: new mongoose.Types.ObjectId('673317a4a25b6e3e5e2bb410'), moduelsId: '672c5c4a05e65803246ffc33', routePath: '/v1/auth/changeSubadminStatus' },
  { _id: new mongoose.Types.ObjectId('673317aca25b6e3e5e2bb411'), moduelsId: '672c5c4a05e65803246ffc33', routePath: '/v1/auth/updateSubAdminPermissions' },
  { _id: new mongoose.Types.ObjectId('673317b5a25b6e3e5e2bb412'), moduelsId: '672c5c4a05e65803246ffc33', routePath: '/v1/auth/subAdminList' },
  { _id: new mongoose.Types.ObjectId('673317bea25b6e3e5e2bb413'), moduelsId: '672c5c4a05e65803246ffc33', routePath: '/v1/auth/getModulesListById' },
  { _id: new mongoose.Types.ObjectId('673317c6a25b6e3e5e2bb414'), moduelsId: '672c5c4a05e65803246ffc33', routePath: '/v1/auth/getSubadminListCsv' },
  { _id: new mongoose.Types.ObjectId('673320c1db988f336df84b33'), moduelsId: '672c705305e65803246ffc40', routePath: '/v1/auth/getIssuerdata' },
  { _id: new mongoose.Types.ObjectId('673b48a942643c6c89a877e2'), moduelsId: '672c705305e65803246ffc40', routePath: '/v1/auth/approveIssuer' },
  { _id: new mongoose.Types.ObjectId('673b4a0342643c6c89a877f3'), moduelsId: '672c70af05e65803246ffc42', routePath: '/v1/auth/offeringList/' },
  { _id: new mongoose.Types.ObjectId('67484d3f8a57982de7aafb14'), moduelsId: '672c70af05e65803246ffc42', routePath: '/v1/auth/offering/' },
  { _id: new mongoose.Types.ObjectId('6731f51c2cfb7d661da8e76f'), moduelsId: '672c70ee05e65803246ffc46', routePath: '/v1/auth/transactions/:id' },
];

// 5. Migrate data into the 'routes' collection, only if the route doesn't already exist
async function migrateRoutes() {
  try {
    // Step 1: Find the existing routes in the collection
    const existingRoutes = await frontendroutesSchema.find({
      _id: { $in: routeData.map((item) => item._id) },
    });

    // Step 2: Extract the existing IDs
    const existingIds = existingRoutes.map((route: { _id: { toString: () => any } }) => route._id.toString());

    // Step 3: Filter out the items that already exist based on the _id
    const newRoutesToInsert = routeData.filter((item) => !existingIds.includes(item._id.toString()));

    // Step 4: If there are new routes to insert, insert them into the collection
    if (newRoutesToInsert.length > 0) {
      const result = await frontendroutesSchema.insertMany(newRoutesToInsert);
    } else {
      console.log('No new routes to insert. All records already exist.');
    }
  } catch (error) {
    console.error('Error migrating data:', error);
  }
}

// 6. Call the migration function
module.exports = migrateRoutes;
