import { Router } from 'express';
import { transferAgentComponent } from '../component';
import { BlockTransferAgent, CreateTransferAgent } from '../middleware';
import { checkPermissions } from '../middleware/checkAdminPermissions';

// import { isIssuerCompleted, isKycCompleted } from '../config/middleware/jwtAuthenticated';

/**
 * @constant {express.Router}
 */
const router: Router = Router();
router.post('/createTransferAgent', checkPermissions, CreateTransferAgent, transferAgentComponent.createTransferAgent);
router.get('/transferAgentList', checkPermissions, transferAgentComponent.getTransferAgentListController);
router.get('/transferAgent', transferAgentComponent.getTransferAgentController);
router.post('/changeStatus', checkPermissions, BlockTransferAgent, transferAgentComponent.unblock);
router.get('/transferAgentListCsv', checkPermissions, transferAgentComponent.transferAgentListCsv);

/*
 * @export {express.Router}
 */

export default router;
