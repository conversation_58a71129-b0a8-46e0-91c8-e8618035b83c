import { Router } from 'express';
import representativeComponent from '../component/representatives';
import { checkPermissions } from '../middleware/checkAdminPermissions';
import {
  validateCreateRepresentativeReq,
  validateUpdateRepresentativeReq,
  validateBlockUnblockRepresentativeReq,
  validateGetRepresentativeListReq,
  validateRepresentativeIdReq,
} from '../middleware/representative.middleware';

/**
 * @constant {express.Router}
 */
const router: Router = Router();

/**
 * @description Create a new representative
 * @route POST /representatives
 * @access Private (Admin only)
 */
router.post('/representatives', checkPermissions, validateCreateRepresentativeReq, representativeComponent.createRepresentativeController);

/**
 * @description Get list of representatives with pagination and filters
 * @route GET /representatives
 * @access Private (Admin only)
 */
router.get('/representatives', checkPermissions, validateGetRepresentativeListReq, representativeComponent.getRepresentativeListController);

/**
 * @description Get representatives list for CSV export
 * @route GET /representatives/csv
 * @access Private (Admin only)
 */
router.get('/representatives/csv', checkPermissions, validateGetRepresentativeListReq, representativeComponent.getRepresentativeListCsvController);

/**
 * @description Get a specific representative by ID
 * @route GET /representatives/:id
 * @access Private (Admin only)
 */
router.get('/representatives/:id', checkPermissions, validateRepresentativeIdReq, representativeComponent.getRepresentativeController);

/**
 * @description Update a representative
 * @route PUT /representatives/:id
 * @access Private (Admin only)
 */
router.put('/representatives/:id', checkPermissions, validateRepresentativeIdReq, validateUpdateRepresentativeReq, representativeComponent.updateRepresentativeController);

/**
 * @description Block or unblock a representative (set isDeleted flag)
 * @route POST /representatives/block-unblock
 * @access Private (Admin only)
 */
router.post('/representatives/block-unblock', checkPermissions, validateBlockUnblockRepresentativeReq, representativeComponent.blockUnblockRepresentativeController);

/**
 * @description Soft delete a representative (set isActive to false)
 * @route DELETE /representatives/:id
 * @access Private (Admin only)
 */
router.delete('/representatives/:id', checkPermissions, validateRepresentativeIdReq, representativeComponent.deleteRepresentativeController);

/**
 * @export {express.Router}
 */
export default router;
