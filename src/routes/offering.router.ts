import { Router } from 'express';
import { OfferingComponent } from '../component';
import { checkPermissions } from '../middleware/checkAdminPermissions';
import { createOfferingsValidationReq, offeringReportValidationReq } from '../middleware/offering.middleware';

/**
 * @constant {express.Router}
 */
const router: Router = Router();

router.get('/offering/manage-issuers', checkPermissions, OfferingComponent.getIssuerOfferingController);
router.get('/offeringList', OfferingComponent.getOfferingListController);
router.get('/getOfferingList', OfferingComponent.getOfferingList);
router.get('/getOfferingListCsv', checkPermissions, OfferingComponent.getOfferingList);

router.get('/offering', checkPermissions, OfferingComponent.getOfferingDetailsController);
router.post('/rejectOffering', checkPermissions, OfferingComponent.rejectOffering);
router.get('/adminGraph', offeringReportValidationReq, OfferingComponent.offeringReportController);
router.get('/offering/requested-offerings', OfferingComponent.requestedOfferings);
router.get('/offering/requested-offeringsCsv', OfferingComponent.requestedOfferingsCsv);
router.put('/offering/assign', OfferingComponent.assignOfferings);
router.post('/offering', checkPermissions, createOfferingsValidationReq, OfferingComponent.createOfferingController);

/**
 * @export {express.Router}
 */

export default router;
