import { Router } from 'express';
import { AuthComponent } from '../component';
import { validateEmailReq, validateVerificationReq, validateLoginReq, resendOtpValidationReq, validateResetPasswordReq, verify2FAReq, tokenValidationReq } from '../middleware';
import authMiddleware from '../config/middleware/jwtTokenAuth';
/**
 * @constant {express.Router}
 */
const router: Router = Router();
router.post('/login', validateLoginReq, AuthComponent.loginController);
router.post('/verify', validateVerificationReq, AuthComponent.verifyController);
router.post('/resend-otp', resendOtpValidationReq, AuthComponent.reSendOtpController);
router.post('/forgot-password', validateEmailReq, AuthComponent.forgotPasswordController);
router.post('/verify-2faBeforelogin', authMiddleware, AuthComponent.verify2FALcoginController);
router.post('/reset-password', validateResetPasswordReq, authMiddleware, AuthComponent.resetPasswordController);
router.post('/verify-2fas', verify2FAReq, authMiddleware, AuthComponent.verifyLogin2FAController);
router.patch('/forgot-2fa', tokenValidationReq, authMiddleware, AuthComponent.forgot2FAController);
router.post('/reset2FA', tokenValidationReq, authMiddleware, AuthComponent.reSet2FA);

/**
 * @export {express.Router}
 */
export default router;
