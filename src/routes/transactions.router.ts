import { Router } from 'express';
import * as jwtAuthenticated from '../config/middleware/jwtAuthenticated';
import { convertValidationReq, transactionsValidationReq } from '../middleware/transactions.middleware';
import { TransactionController } from '../component';

/**
 * @constant {express.Router}
 */

const router: Router = Router();
router.use(jwtAuthenticated.isAuthenticated);

router.get('/transactions/:offeringId', transactionsValidationReq, TransactionController.getTransactions);
router.get('/transactionsCsv/:offeringId', TransactionController.getTransactionsCsv);
/**
 * @export {express.Router}
 */

export default router;
