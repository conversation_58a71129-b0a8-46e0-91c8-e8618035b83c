import { Router } from 'express';
import { AuthComponent } from '../component';
import { tokenValidationReq, getOfferingNotification, seenOfferingNotification } from '../middleware';
import authMiddleware from '../config/middleware/jwtTokenAuth';
import { Notification } from '../component/index';

/**
 * @constant {express.Router}
 */
const router: Router = Router();

router.get('/', getOfferingNotification, Notification.getOfferingNotification);
router.post('/seen', seenOfferingNotification, Notification.seenOfferingNotification);

export default router;
