import { Router } from 'express';
import { subadminComponent } from '../component';
import { BlockTransferAgent, CreateSubAdmin, UpdateSubAdmin } from '../middleware';
import { checkPermissions } from '../middleware/checkAdminPermissions';

// import { isIssuerCompleted, isKycCompleted } from '../config/middleware/jwtAuthenticated';
/**
 * @constant {express.Router}
 */
const router: Router = Router();
router.post('/createSubadmin', checkPermissions, CreateSubAdmin, subadminComponent.createSubadmin);
router.get('/loggedInUserModuleList', subadminComponent.moduleList);
router.get('/getModuleList', checkPermissions, subadminComponent.getModuleListById);
router.post('/changeSubadminStatus', checkPermissions, subadminComponent.unblock);
router.post('/updateSubAdminPermissions', checkPermissions, UpdateSubAdmin, subadminComponent.updateStatus);
router.get('/subAdminList', checkPermissions, subadminComponent.subAdminList);
router.get('/getModulesListById', checkPermissions, subadminComponent.getPermissionListById);
router.get('/getSubadminListCsv', checkPermissions, subadminComponent.getSubadminListControllerCsv);

/**
 * @export {express.Router}
 */

export default router;
