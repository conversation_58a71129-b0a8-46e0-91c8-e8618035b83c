import { Router } from 'express';
import { AuthComponent } from '../component';
import { validateGetUsersList, validateGetissuerUsersList } from '../middleware/validateGetUsersList';
import { approveIssuer, approveUser, changePasswordReq, docsValidationReq, tokenValidationReq } from '../middleware';
import { checkPermissions } from '../middleware/checkAdminPermissions';
import * as file from '../middleware/googleCloud.middleware';
/**
 * @constant {express.Router}
 */
// getUserslist
const router: Router = Router();
router.get('/getUserslist', checkPermissions, validateGetUsersList, AuthComponent.getUsersListController);
router.get('/getUsersListCsv', checkPermissions, AuthComponent.getUsersListControllerCsv);
router.post('/approve', checkPermissions, approveUser, AuthComponent.approve);
router.get('/getFeeList', AuthComponent.getFeeList);
router.get('/getWalletDetails', AuthComponent.curentFee);
router.post('/approveIssuer', checkPermissions, approveIssuer, AuthComponent.approveIssuer);
router.get('/getUserdata', AuthComponent.getUserdata);
router.post('/unblock', checkPermissions, AuthComponent.unblock);
router.patch('/change-password', changePasswordReq, AuthComponent.changePasswordController);
router.get('/enable-2fa', AuthComponent.enable2FAController);
router.post('/verify-2fa', AuthComponent.verify2FAController);
router.patch('/disable-2fa', tokenValidationReq, AuthComponent.disable2FAController);
router.get('/log-out', AuthComponent.logOutController);
router.get('/getissuer', checkPermissions, validateGetissuerUsersList, AuthComponent.getIssuerController);
router.get('/getIssuerListCsv', checkPermissions, checkPermissions, AuthComponent.getIssuerListControllerCsv);
router.get('/getManageIssuerListCsv', checkPermissions, AuthComponent.getManageIssuerListCsv);
router.get('/dashboard', AuthComponent.dashboard);
router.get('/user', AuthComponent.user);
router.get('/topInvestors', AuthComponent.topInvestors);
router.get('/topHoldings', AuthComponent.topInvestors);
router.post('/upload-docs', file.validateFiles, docsValidationReq, AuthComponent.uploadDocs);
router.get('/report', AuthComponent.singleOfferingReportController);

/**
 * @export {express.Router}
 */

export default router;
