import * as multer from 'multer';
import { Request, Response, NextFunction } from 'express';
import CONFIG from '../config/env';
import { ResponseHandler } from '../helpers/response.helper';
import { RESPONSES } from '../utils/responseUtils';

// Set up memory storage for multer
const storage = multer.memoryStorage();

// Define the type for the file filter callback
type FileFilterCallback = (error: Error | null, acceptFile: boolean) => void;

// Existing filter for JPG, PNG, and PDF files
const imageFilter = (req: Request, file: Express.Multer.File, cb: FileFilterCallback) => {
  if (!file.originalname.match(/\.(JPG|jpg|jpeg|JPEG|png|PNG|PDF|pdf)$/)) {
    return cb(null, false); // Reject the file
  }
  cb(null, true);
};

// New filter for only JPG and PNG file types
const imageOnlyFilter = (req: Request, file: Express.Multer.File, cb: FileFilterCallback) => {
  if (!file.originalname.match(/\.(JPG|jpg|jpeg|JPEG|png|PNG)$/)) {
    return cb(null, false); // Reject the file
  }
  cb(null, true);
};

// Set the maximum file size from the config
const maxSize = { fileSize: Number(CONFIG.GOOGLE.MAX_SIZE) * 1024 * 1024 };

// Middleware to handle multer file uploads and errors
const uploadHandler = (fileFilter: (req: Request, file: Express.Multer.File, cb: FileFilterCallback) => void) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const upload = multer({
      storage,
      fileFilter,
      limits: maxSize,
    }).single('file');

    upload(req, res, (err) => {
      // Handle multer-specific errors
      if (err instanceof multer.MulterError) {
        if (err.code === 'LIMIT_FILE_SIZE') {
          return ResponseHandler.error(res, {
            message: `File size should not exceed ${CONFIG.GOOGLE.MAX_SIZE} MB.`,
            status: RESPONSES.BAD_REQUEST,
            error: true,
          });
        }
      } else if (err) {
        return ResponseHandler.error(res, {
          message: err.message || 'An unknown error occurred during file upload.',
          status: RESPONSES.BAD_REQUEST,
          error: true,
        });
      }
      next();
    });
  };
};

// Export the existing function that validates JPG, PNG, and PDF files
export const validateFiles = uploadHandler(imageFilter);

// Export the new function that validates only JPG and PNG files
export const validateImageFiles = uploadHandler(imageOnlyFilter);
