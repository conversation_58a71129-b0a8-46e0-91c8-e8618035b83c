/* eslint-disable @typescript-eslint/no-explicit-any */
import { Request, Response, NextFunction } from 'express';
import { JoiValidationResult, PromiseResolve } from '../utils/common.interface';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import RepresentativeValidation from '../component/representatives/validation';
import { ResponseHandler } from '../helpers/response.helper';
import logger from '../helpers/logging/logger.helper';
import CustomError from '../helpers/customError.helper';

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function validateCreateRepresentativeReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await RepresentativeValidation.createRepresentativeValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'validateCreateRepresentativeReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function validateUpdateRepresentativeReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await RepresentativeValidation.updateRepresentativeValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'validateUpdateRepresentativeReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function validateBlockUnblockRepresentativeReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await RepresentativeValidation.blockUnblockRepresentativeValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'validateBlockUnblockRepresentativeReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function validateGetRepresentativeListReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await RepresentativeValidation.getRepresentativeListValidation(req.query);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'validateGetRepresentativeListReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function validateRepresentativeIdReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await RepresentativeValidation.representativeIdValidation(req.params);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.params = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'validateRepresentativeIdReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}
