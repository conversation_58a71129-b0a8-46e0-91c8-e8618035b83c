/* eslint-disable @typescript-eslint/no-explicit-any */
import { Request, Response, NextFunction } from 'express';
import { Jo<PERSON><PERSON>alidationResult, PromiseResolve, UserType } from '../utils/common.interface';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import UserValidation from '../component/userAuthentications/validation';
import { ResponseHandler } from '../helpers/response.helper';
import logger from '../helpers/logging/logger.helper';
import CustomError from '../helpers/customError.helper';
import { permissionSchema } from '../component/subadmin/permission.model';
import { frontendroutesSchema } from '../component/subadmin/frontend_routes.model';

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function checkPermissions(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const role = req?.userInfo?.userType;
    const apiType = req.method;
    const email = req?.userInfo?.email;
    let url = req?.originalUrl;
    url = req?.originalUrl.split('?')[0];

    if (role == UserType.Admin) {
      next();
    } else if (role == UserType.Subadmin) {
      const permission: any = await permissionSchema.findOne({ email });
      const moduleIds: any = await frontendroutesSchema.findOne({ routePath: url });
      const foundPermission = permission.permission.find((perm: { moduelsId: { equals: (arg0: any) => any } }) => perm?.moduelsId.equals(moduleIds?.moduelsId));
      console.log('foundPermission---------', foundPermission);
      if (!foundPermission) {
        throw new CustomError(RES_MSG.USER.UNAUTHORIZE, RESPONSES.CONFLICT);
      }
      const { read, write } = foundPermission;
      if (!read && !write) {
        throw new CustomError(RES_MSG.USER.UNAUTHORIZE, RESPONSES.CONFLICT);
      }
      if (read) {
        if (apiType === 'GET') {
          return next(); // Allow access for GET requests if read is true
        } else if (apiType === 'POST' && !write) {
          throw new CustomError(RES_MSG.USER.UNAUTHORIZE, RESPONSES.CONFLICT);
        }
      }
      if (write) {
        return next(); // Allow access if write permission is true, regardless of apiType
      }
    }
  } catch (error: any) {
    logger.error(error, 'validateSignupReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}
