/* eslint-disable @typescript-eslint/no-explicit-any */
export interface ResponsePayLoad<T = void> {
  message: string;
  status: number;
  data?: T;
  error: boolean;
}
export interface PromiseResolve {
  status: number;
  error: boolean;
  message: string;
  data?: any;
}

export interface createJWTPayload {
  userId?: string;
  email?: string;
  userType?: string;
}

export enum paymentTypeEnum {
  USDC = 'USDC',
  USDT = 'USDT',
  ERC20 = 'ERC20',
  ERC3643 = 'ERC3643',
  // BANK_TRANSFER = 'Bank Transfer',
}

export interface JoiValidationResult {
  error: boolean;
  value: any;
  message?: string;
  status?: number;
}

export interface DecodedToken {
  userId: string;
}

export interface MessagePayload {
  userId: string;
  message: string;
  offeringId: string;
}

export interface UserInfo {
  userId: string;
  email: string;
  status: string;
  reason: string;
  userType: string;
  exp: number;
}

export enum UserType {
  Investor = 'investor',
  Institution = 'institution',
  Admin = 'admin',
  Subadmin = 'subadmin',
  Issuer = 'issuer',
  TransferAgent = 'transferAgent',
  IssuerAdmin = 'issuerAdmin',
  IssuerSubadmin = 'issuerSubadmin',
}

export enum sumSubKycStatusEnum {
  NOT_STARTED = 'NOT_STARTED',
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  DECLINED = 'DECLINED',
  HOLD = 'HOLD',
  PRECHECKRED = 'PRECHECKRED',
}
export enum transferAgentStatus {
  REQUESTSENT = 'REQUEST-SENT',
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  DECLINED = 'DECLINED',
}

export const enum queueMessageTypeEnum {
  USER = 'user',
  OFFERING = 'offering',
  REQ_OFFERING = 'reqOffering',
  ORDER = 'order',
}

export enum KycStatus {
  STARTED = 'STARTED',
  NOT_STARTED = 'NOT_STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  RESUBMIT = 'RESUBMIT',
}

export enum IssuerStatus {
  NOT_APPLIED = 'NOT_APPLIED',
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  BLOCKED = 'BLOCKED',
}

export enum transferStatusEnum {
  STARTED = 'STARTED',
  MINTED = 'MINTED',
  BURN = 'BURN',
  FREEZE = 'FREEZE',
  UNFREEZE = 'UNFREEZE',
  TRANSFER = 'TRANSFER',
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  RESUBMIT = 'RESUBMIT',
  DELETED = 'DELETED',
}
export enum offeringStatusEnum {
  REQUESTED = 'REQUESTED',
  STARTED = 'STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  RESUBMIT = 'RESUBMIT',
  MINTED = 'MINTED',
  DELETED = 'DELETED',
  REVIEW = 'REVIEW',
  ASSIGNED = 'ASSIGNED',
}
export enum orderStatusEnum {
  STARTED = 'STARTED',
  MINTED = 'MINTED',
  BURN = 'BURN',
  FREEZE = 'FREEZE',
  UNFREEZE = 'UNFREEZE',
  TRANSFER = 'TRANSFER',
  PENDING = 'PENDING',
  ALL = 'ALL',
  REDEEM = 'REDEEM',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  RESUBMIT = 'RESUBMIT',
  DELETED = 'DELETED',
  TRANSFER_FROM = 'TRANSFER_FROM',
  TRANSFER_TO = 'TRANSFER_TO',
  CONVERT_FROM = 'CONVERT_FROM',
  CONVERT_TO = 'CONVERT_TO',
  CONVERT = 'CONVERT',
  PRICE_CHANGE = 'PRICE_CHANGE',
  PRICE_CANCELLED = 'CANCELLED',
  INITIAL_ORDER = 'INITIAL_ORDER',
}

export enum kafkaTypeEnum {
  ADMINWALLETUPDATED = 'AdminWalletUpdated',
  OFFERING = 'offering',
  REQOFFERING = 'reqOffering',
  REQWHITELIST = 'reqWhitelist',
  ORDER = 'order',
  SAVEEVENT = 'saveEvent',
  WHITELIST = 'whitelist',
  USER = 'user',
  FORCETRANSFERRED = 'ForceTransferred',
  COUNTRYALLOW = 'countryAllow',
  COUNTRYRESTRICT = 'countryRestrict',
}

export enum soketTypeEnum {
  UPDATEKYCSTATUS = 'updateKycStatus',
  ADMINFEEUPDATED = 'adminFeeUpdated',
  UPDATEFORCETRANSFERRED = 'updateForceTransferred',
}

export enum OperationType {
  READ = 0,
  READ_WRITE = 1,
  NONE = 2,
}

export enum AssetsType {
  RealEstate = 'Real Estate',
  PrivateEquity = 'Private Equity',
  TreasuryBonds = 'Treasury Bonds',
  ArtCollectibles = 'Art & Collectibles',
  MBS = 'MBS',
  Commodities = 'Commodities',
}

export enum ChainType {
  METAMASK_WALLET = 'MetamaskWallet',
  TRUST_WALLET = 'TrustWallet',
  WALLET_CONNECT = 'WalletConnect',
  FIREBLOCKS_WALLET = 'FireblocksWallet',
}

export const otpType = {
  LOGIN: 'login',
  FORGOT: 'forgot',
  TFA: 'tfa',
};

export enum DocumentTypes {
  FRONT_ID_CARD = 'frontId',
  BACK_ID_CARD = 'backId',
  NATIONAL_ID = 'proofOfResidence',
}

export interface IUserFilters {
  name?: string;
  email?: string;
  mobile?: string;
  isActive?: boolean;
  isDeleted?: boolean;
  kycStatus?: string;
  userType?: string;
  countryCode?: string;
  isKyc?: boolean;
}

export interface IPagination {
  page: number;
  limit: number;
  sort?: any;
  search?: any;
}

export interface IUserListResult {
  data: any[];
  currentPage: number;
  totalPages: number;
  totalCount: number;
  nextPage: number | null;
  previousPage: number | null;
}

export enum AssetTypeEnum {
  RealEstate = 'Real Estate',
  Equity = 'Equity',
  Debt = 'Debt',
  Others = 'Others',
}

export enum EntityTypeEnum {
  IndividualPerson = 'Individual (Person)',
  SoleProprietorship = 'Sole Proprietorship',
  Partnership = 'Partnership',
  Corporation = 'Corporation (or Company)',
  LLC = 'Limited Liability Company (LLC)',
  NonProfitOrganization = 'Non-Profit Organization',
  GovernmentEntity = 'Government Entity',
  Trust = 'Trust',
  JointVenture = 'Joint Venture',
  Association = 'Association',
  Cooperative = 'Cooperative',
  PubliclyTradedCompany = 'Publicly Traded Company',
  PrivateCompany = 'Private Company',
  EducationalInstitution = 'Educational Institution',
  Foundation = 'Foundation',
}

export enum IncomeSourceEnum {
  EmploymentIncome = 'Employment Income',
  BusinessIncome = 'Business Income',
  InvestmentIncome = 'Investment Income',
  Savings = 'Savings',
  Inheritance = 'Inheritance',
  Gift = 'Gift',
  SaleOfPropertyOrAssets = 'Sale of Property or Assets',
  LoanOrCredit = 'Loan or Credit',
  PensionRetirementFund = 'Pension/Retirement Fund',
  Dividends = 'Dividends',
  GovernmentAssistanceBenefits = 'Government Assistance/Benefits',
}

export enum BlockchainEnum {
  Base = 'Base',
}
export enum OfferingTypeEnum {
  PublicOffering = 'Public Offering',
  PrivatePlacement = 'Private Placement',
  ShareCapitalManagement = 'Share Capital Management',
}

export enum StandardTokenEnum {
  ERC3643 = 'ERC-3643',
}

export enum PropertyTypeEnum {
  Commercial = 'Commercial',
  Residential = 'Residential',
  Office = 'Office',
  Multifamily = 'Multifamily',
  Retail = 'Retail',
  Hotel = 'Hotel',
  Industrial = 'Industrial',
  Others = 'Others',
}

export enum offeringDocumentTypesEnum {
  eSign = 'eSign',
  pitchDeck = 'pitchDeck',
  confidentialInformationMemorendum = 'confidentialInformationMemorendum',
  landRegistration = 'landRegistration',
  titleDocs = 'titleDocs',
  bankApproval = 'bankApproval',
  encumbranceCertificate = 'encumbranceCertificate',
  propertyTaxReceipt = 'propertyTaxReceipt',
  articlesOfAssociation = 'articlesOfAssociation',
  operatingAgreement = 'operatingAgreement',
  taxAssignmentLetter = 'taxAssignmentLetter',
  certificateOfRegistration = 'certificateOfRegistration',
  registerOfManagers = 'registerOfManagers',
  icon = 'icon',
  companyLogo = 'companyLogo',
  offeringCoverImage = 'offeringCoverImage',

  // eSignatureDocument = 'eSignatureDocument',
  // titleDocument = 'titleDocument',
  // actualSite = 'actualSite',
  // salesDeed = 'salesDeed',
  // powerOfAttorney = 'powerOfAttorney',
  // propertyUnderSociety = 'propertyUnderSociety',
  url = 'url',
  customImage = 'customImage',
}

export enum DocumentTypesEnum {
  FRONT_ID_CARD = 'frontId',
  BACK_ID_CARD = 'backId',
  NATIONAL_ID = 'proofOfResidence',
  PROFILE = 'profile',
  DIVIDEND = 'DIVIDEND',
  PROPOSAL = 'proposal',
}
export enum DocumentFolderTypesEnum {
  USER = 'users',
  OFFERING = 'offering',
  DIVIDEND = 'DIVIDEND',
  PROPOSAL = 'proposal',
}
