export const swaggerDefinition: any = {
  info: {
    title: 'Valuit Tokenization',
    description:
      'Tokenization is the process of converting rights to an asset into a digital token on a blockchain. This method allows for the representation, management, and transfer of assets in a secure and decentralized manner.',
    version: '1.0.0',
  },
  tags: [],
  securityDefinitions: {
    bearerAuth: {
      type: 'api<PERSON><PERSON>',
      name: 'Authorization',
      scheme: 'bearer',
      in: 'header',
    },
  },
  security: [
    {
      bearerAuth: [],
    },
  ],
  paths: {
    '/v1/login': {
      post: {
        summary: 'login',
        deprecated: false,
        description:
          'This is a POST request, submitting data to an API via the request body. This request submits JSON data, and the data is reflected in the response.\n\nA successful POST request typically returns a `200 OK` or `201 Created` response code.',
        tags: [],
        parameters: [
          {
            name: 'body',
            in: 'body',
            schema: {
              type: 'object',
              properties: {
                email: { type: 'string', example: '<EMAIL>' },
                password: { type: 'string', example: '<PERSON><PERSON>@123' },
              },
              required: ['email', 'password'],
            },
          },
        ],
        responses: {
          0: {
            description: '401',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                data: { type: 'null' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
          'x-0:200': {
            description: '200',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                data: {
                  type: 'object',
                  properties: { isOtpActive: { type: 'boolean' } },
                  required: ['isOtpActive'],
                },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
          'x-0:200-without-login-otp': {
            description: '200-without-login-otp',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                data: {
                  type: 'object',
                  properties: {
                    refreshToken: { type: 'string' },
                    accessToken: { type: 'string' },
                    is2FAActive: { type: 'boolean' },
                  },
                  required: ['refreshToken', 'accessToken', 'is2FAActive'],
                },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
        },
        consumes: ['application/json'],
        produces: ['application/json'],
      },
    },

    '/v1/verify': {
      post: {
        summary: 'verify-otp',
        deprecated: false,
        description:
          'This is a POST request, submitting data to an API via the request body. This request submits JSON data, and the data is reflected in the response.\n\nA successful POST request typically returns a `200 OK` or `201 Created` response code.',
        tags: [],
        parameters: [
          {
            name: 'body',
            in: 'body',
            schema: {
              type: 'object',
              properties: {
                otp: { type: 'string' },
                email: { type: 'string' },
                type: { type: 'string' },
              },
              required: ['otp', 'email', 'type'],
            },
          },
        ],
        responses: {
          0: {
            description: '201',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                data: {
                  type: 'object',
                  properties: {
                    refreshToken: { type: 'string' },
                    accessToken: { type: 'string' },
                  },
                  required: ['refreshToken', 'accessToken'],
                },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
          'x-0:400': {
            description: '400',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                data: { type: 'null' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
        },
        consumes: ['application/json'],
        produces: ['application/json'],
      },
    },
    '/v1/resend-otp': {
      post: {
        summary: 'Resend OTP',
        deprecated: false,
        description:
          "This is a POST request to resend the OTP for user actions like login or registration. The request submits JSON data in the body, which includes the user's email and the type of OTP (e.g., for login).",
        tags: [],
        parameters: [
          {
            name: 'body',
            in: 'body',
            schema: {
              type: 'object',
              properties: {
                email: { type: 'string' },
                type: { type: 'string', enum: ['login', 'registration', 'reset-password'] },
              },
              required: ['email', 'type'],
            },
          },
        ],
        responses: {
          '200': {
            description: 'OTP resent successfully',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                data: { type: 'null' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
          '400': {
            description: 'Bad Request - Invalid email or missing type',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'error'],
            },
          },
        },
        consumes: ['application/json'],
        produces: ['application/json'],
      },
    },

    '/v1/forgot-password': {
      post: {
        summary: 'reset-password',
        deprecated: false,
        description:
          'This is a POST request, submitting data to an API via the request body. This request submits JSON data, and the data is reflected in the response.\n\nA successful POST request typically returns a `200 OK` or `201 Created` response code.',
        tags: [],
        parameters: [
          {
            name: 'body',
            in: 'body',
            schema: {
              type: 'object',
              properties: { email: { type: 'string' } },
              required: ['email'],
            },
          },
        ],
        responses: {
          0: {
            description: 'forget-password',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                data: { type: 'null' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
        },
        consumes: ['application/json'],
        produces: ['application/json'],
      },
    },

    '/v1/auth/approve': {
      post: {
        summary: 'Approve or Reject KYC',
        deprecated: false,
        description:
          'This is a POST request for approving or rejecting KYC based on the submitted data. The data is submitted as JSON in the request body. A successful POST request typically returns a `200 OK` or `201 Created` response code.',
        tags: [],
        parameters: [
          {
            name: 'Authorization',
            in: 'header',
            required: true,
            type: 'string',
            description: 'Bearer token',
            example: 'Bearer <token>',
          },
          {
            name: 'body',
            in: 'body',
            schema: {
              type: 'object',
              properties: {
                email: { type: 'string' },
                kycStatus: { type: 'string', enum: ['APPROVED', 'REJECTED'] },
                kycReason: { type: 'string', nullable: true },
              },
              required: ['email', 'kycStatus'],
            },
          },
        ],
        responses: {
          '200': {
            description: 'KYC status updated successfully',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                data: { type: 'null' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
        },
        consumes: ['application/json'],
        produces: ['application/json'],
      },
    },

    '/v1/auth/approveIssuer': {
      post: {
        summary: 'Approve or Reject Issuer',
        deprecated: false,
        description:
          'This is a POST request for approving or rejecting an issuer based on the submitted data. The data is submitted as JSON in the request body. A successful POST request typically returns a `200 OK` or `201 Created` response code.',
        tags: [],
        parameters: [
          {
            name: 'Authorization',
            in: 'header',
            required: true,
            type: 'string',
            description: 'Bearer token',
            example: 'Bearer <token>',
          },
          {
            name: 'body',
            in: 'body',
            schema: {
              type: 'object',
              properties: {
                email: { type: 'string' },
                issuerStatus: { type: 'string', enum: ['APPROVED', 'REJECTED'] },
                issuerReason: { type: 'string', nullable: true },
              },
              required: ['email', 'issuerStatus'],
            },
          },
        ],
        responses: {
          '200': {
            description: 'Issuer status updated successfully',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                data: { type: 'null' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
        },
        consumes: ['application/json'],
        produces: ['application/json'],
      },
    },

    '/v1/auth/unblock': {
      post: {
        summary: 'Block or Unblock a user',
        deprecated: false,
        description:
          'This is a POST request for blocking or unblocking a user based on the submitted data. If `isActive` is `true`, the user will be unblocked. If `isActive` is `false`, the user will be blocked. The data is submitted as JSON in the request body. A successful POST request typically returns a `200 OK` response code.',
        tags: [],
        parameters: [
          {
            name: 'Authorization',
            in: 'header',
            required: true,
            type: 'string',
            description: 'Bearer token',
            example: 'Bearer <token>',
          },
          {
            name: 'body',
            in: 'body',
            schema: {
              type: 'object',
              properties: {
                email: { type: 'string', description: 'Email of the user to block or unblock' },
                isActive: { type: 'string', description: '`true` to unblock the user, `false` to block the user' },
              },
              required: ['email', 'isActive'],
            },
          },
        ],
        responses: {
          '200': {
            description: 'User status updated successfully',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                data: { type: 'null' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
          '400': {
            description: 'Invalid request or user not found',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'error'],
            },
          },
        },
        consumes: ['application/json'],
        produces: ['application/json'],
      },
    },

    '/v1/auth/enable-2fa': {
      get: {
        summary: 'Enable 2FA',
        deprecated: false,
        description: 'This is a GET request to enable 2FA. The request requires the `Authorization` token in the header. A successful request typically returns a `200 OK` response code.',
        tags: ['Authentication'],
        parameters: [
          {
            name: 'Authorization',
            in: 'header',
            required: true,
            type: 'string',
            description: 'Bearer token',
            example: 'Bearer <token>',
          },
        ],
        responses: {
          '200': {
            description: '2FA enabled successfully',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                data: {
                  type: 'object',
                  properties: {
                    qrCode: { type: 'string' },
                  },
                  required: ['qrCode'],
                },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
          '400': {
            description: 'Invalid request or missing token',
          },
        },
        produces: ['application/json'],
      },
    },

    '/v1/auth/log-out': {
      get: {
        summary: 'logOut',
        deprecated: false,
        description:
          'This is a GET request for logging out the user. The request requires the `email` in the request body and the `Authorization` token in the header. A successful request typically returns a `200 OK` response code.',
        tags: [],
        parameters: [
          {
            name: 'Authorization',
            in: 'header',
            required: true,
            type: 'string',
            description: 'Bearer token',
            example: 'Bearer <token>',
          },
          {
            name: 'body',
            in: 'body',
            schema: {
              type: 'object',
              properties: {
                email: { type: 'string' },
              },
              required: ['email'],
            },
          },
        ],
        responses: {
          '200': {
            description: 'Log out successful',
            schema: {
              type: 'object',
              properties: {
                status: { type: 'integer' },
                error: { type: 'boolean' },
                message: { type: 'string' },
              },
              required: ['status', 'error', 'message'],
            },
          },
        },
        consumes: ['application/json'],
        produces: ['application/json'],
      },
    },

    '/v1/auth/disable-2fa': {
      post: {
        summary: 'Disable 2FA',
        deprecated: false,
        description:
          'This is a POST request to disable Two-Factor Authentication (2FA) for a user. The data is submitted as JSON in the request body. A successful POST request typically returns a `200 OK` or `204 No Content` response code.',
        tags: [],
        parameters: [
          {
            name: 'Authorization',
            in: 'header',
            required: true,
            type: 'string',
            description: 'Bearer token',
            example: 'Bearer <token>',
          },
          {
            name: 'body',
            in: 'body',
            schema: {
              type: 'object',
              properties: {
                token: { type: 'string', description: '2FA token to disable' },
              },
              required: ['token'],
            },
          },
        ],
        responses: {
          '200': {
            description: '2FA disabled successfully',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                data: { type: 'null' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
          '400': {
            description: 'Invalid or expired token',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'error'],
            },
          },
        },
        consumes: ['application/json'],
        produces: ['application/json'],
      },
    },
    '/v1/forgot-2fa': {
      patch: {
        summary: 'Request Forgot 2FA',
        deprecated: false,
        description:
          'This is a POST request to initiate the forgot 2FA process. The data is submitted as JSON in the request body. A successful POST request typically returns a `200 OK` or `201 Created` response code.',
        tags: [],
        parameters: [
          {
            name: 'body',
            in: 'body',
            schema: {
              type: 'object',
              properties: {
                token: { type: 'string', description: 'JWT token for user verification' },
              },
              required: ['token'],
            },
          },
        ],
        responses: {
          '200': {
            description: 'Forgot 2FA process initiated successfully',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                data: { type: 'null' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
          '400': {
            description: 'Invalid or expired token',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'error'],
            },
          },
        },
        consumes: ['application/json'],
        produces: ['application/json'],
      },
    },
    '/v1/verify-2faBeforelogin': {
      post: {
        summary: 'Verify 2FA Before Login',
        description: 'This is a POST request to verify the provided 2FA token and code before the user can log in.',
        parameters: [
          {
            name: 'body',
            in: 'body',
            required: true,
            schema: {
              type: 'object',
              properties: {
                token: {
                  type: 'string',
                  description: 'JWT token for user verification',
                },
                code: {
                  type: 'string',
                  description: 'Verification code for 2FA',
                },
              },
              required: ['token', 'code'],
            },
          },
        ],
        responses: {
          '200': {
            description: '2FA verification before login successful',
            schema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                },
                status: {
                  type: 'integer',
                },
                data: {
                  type: 'null',
                },
                error: {
                  type: 'boolean',
                },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
          '400': {
            description: 'Invalid or expired token/code',
            schema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                },
                status: {
                  type: 'integer',
                },
                error: {
                  type: 'boolean',
                },
              },
              required: ['message', 'status', 'error'],
            },
          },
        },
        consumes: ['application/json'],
        produces: ['application/json'],
      },
    },

    '/verify-2fas': {
      post: {
        summary: 'Verify 2FA',
        description: 'This is a POST request to verify the provided 2FA token and OTP.',
        parameters: [
          {
            name: 'body',
            in: 'body',
            required: true,
            schema: {
              type: 'object',
              properties: {
                token: {
                  type: 'string',
                  description: 'JWT token for user verification',
                },
                otp: {
                  type: 'string',
                  description: 'One-time password for 2FA verification',
                },
              },
              required: ['token', 'otp'],
            },
          },
        ],
        responses: {
          '200': {
            description: '2FA verification successful',
            schema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                },
                status: {
                  type: 'integer',
                },
                data: {
                  type: 'null',
                },
                error: {
                  type: 'boolean',
                },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
          '400': {
            description: 'Invalid or expired token',
            schema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                },
                status: {
                  type: 'integer',
                },
                error: {
                  type: 'boolean',
                },
              },
              required: ['message', 'status', 'error'],
            },
          },
        },
        consumes: ['application/json'],
        produces: ['application/json'],
      },
    },

    '/v1/auth/verify-2fa': {
      post: {
        summary: 'Verify 2FA Token',
        deprecated: false,
        description:
          'This is a POST request for verifying a 2FA (Two-Factor Authentication) token. The data is submitted as JSON in the request body. A successful POST request typically returns a `200 OK` or `201 Created` response code.',
        tags: [],
        parameters: [
          {
            name: 'Authorization',
            in: 'header',
            required: true,
            type: 'string',
            description: 'Bearer token',
            example: 'Bearer <token>',
          },
          {
            name: 'body',
            in: 'body',
            schema: {
              type: 'object',
              properties: {
                token: { type: 'string', description: '2FA token' },
              },
              required: ['token'],
            },
          },
        ],
        responses: {
          '200': {
            description: '2FA token verified successfully',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                data: { type: 'null' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
          '400': {
            description: 'Invalid or expired token',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'error'],
            },
          },
        },
        consumes: ['application/json'],
        produces: ['application/json'],
      },
    },
    '/v1/auth/getUserdata/{userId}': {
      get: {
        summary: 'Get User Data by User ID',
        deprecated: false,
        description: 'This is a GET request to retrieve user data based on the provided user ID. The request requires a valid Bearer token in the Authorization header.',
        tags: [],
        parameters: [
          {
            name: 'Authorization',
            in: 'header',
            required: true,
            type: 'string',
            description: 'Bearer token',
            example: 'Bearer <token>',
          },
          {
            name: 'userId',
            in: 'path',
            required: true,
            type: 'string',
            description: 'The ID of the user whose data is being retrieved',
            example: '66f109fabfffada3bec13652',
          },
        ],
        responses: {
          '200': {
            description: 'User data retrieved successfully',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                data: {
                  type: 'object',
                  properties: {
                    userId: { type: 'string' },
                    email: { type: 'string' },
                    name: { type: 'string' },
                    kycStatus: { type: 'string' },
                    isActive: { type: 'boolean' },
                  },
                },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
          '401': {
            description: 'Unauthorized - Invalid or missing token',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'error'],
            },
          },
          '404': {
            description: 'User not found',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'error'],
            },
          },
        },
        produces: ['application/json'],
      },
    },
    '/v1/auth/getUserslist': {
      get: {
        summary: 'Get Users List',
        deprecated: false,
        description: 'This is a GET request to retrieve a paginated list of users based on the provided query parameters. The request requires a valid Bearer token in the Authorization header.',
        tags: [],
        parameters: [
          {
            name: 'Authorization',
            in: 'header',
            required: true,
            type: 'string',
            description: 'Bearer token',
            example: 'Bearer <token>',
          },
          {
            name: 'page',
            in: 'query',
            required: false,
            type: 'integer',
            description: 'The page number for pagination',
            example: 1,
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            type: 'integer',
            description: 'The number of users to return per page',
            example: 10,
          },
          {
            name: 'name',
            in: 'query',
            required: false,
            type: 'string',
            description: 'The name of the user to search',
            example: 'Amit',
          },
          {
            name: 'userType',
            in: 'query',
            required: false,
            type: 'string',
            description: 'The type of user (e.g., investor, admin)',
            example: 'investor',
          },
          {
            name: 'isActive',
            in: 'query',
            required: false,
            type: 'boolean',
            description: 'Filter by active status (true or false)',
            example: true,
          },
          {
            name: 'sort',
            in: 'query',
            required: false,
            type: 'string',
            description: 'Sorting criteria in JSON format',
            example: '{"createdAt":-1}',
          },
        ],
        responses: {
          '200': {
            description: 'Users list retrieved successfully',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                data: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      userId: { type: 'string' },
                      email: { type: 'string' },
                      name: { type: 'string' },
                      userType: { type: 'string' },
                      isActive: { type: 'boolean' },
                      createdAt: { type: 'string', format: 'date-time' },
                    },
                  },
                },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
          '401': {
            description: 'Unauthorized - Invalid or missing token',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'error'],
            },
          },
        },
        produces: ['application/json'],
      },
    },

    '/v1/auth/getissuer': {
      get: {
        summary: 'Get Issuer List',
        deprecated: false,
        description: 'This is a GET request to retrieve a paginated list of issuers. The request requires a valid Bearer token in the Authorization header.',
        tags: [],
        parameters: [
          {
            name: 'Authorization',
            in: 'header',
            required: true,
            type: 'string',
            description: 'Bearer token',
            example: 'Bearer <token>',
          },
          {
            name: 'page',
            in: 'query',
            required: false,
            type: 'integer',
            description: 'The page number for pagination',
            example: 1,
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            type: 'integer',
            description: 'The number of issuers to return per page',
            example: 10,
          },
        ],
        responses: {
          '200': {
            description: 'Issuer list retrieved successfully',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                data: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      issuerId: { type: 'string' },
                      email: { type: 'string' },
                      name: { type: 'string' },
                      issuerStatus: { type: 'string' },
                      createdAt: { type: 'string', format: 'date-time' },
                    },
                  },
                },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
          '401': {
            description: 'Unauthorized - Invalid or missing token',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'error'],
            },
          },
        },
        produces: ['application/json'],
      },
    },

    '/v1/auth/change-password': {
      patch: {
        summary: 'Change Password',
        deprecated: false,
        description: "This is a PATCH request for changing the user's password. The request submits JSON data in the body and includes a Bearer token for authorization.",
        tags: [],
        parameters: [
          {
            name: 'Authorization',
            in: 'header',
            required: true,
            type: 'string',
            description: 'Bearer token',
            example: 'Bearer <token>',
          },
          {
            name: 'body',
            in: 'body',
            schema: {
              type: 'object',
              properties: {
                oldPassword: { type: 'string' },
                password: { type: 'string' },
              },
              required: ['oldPassword', 'password'],
            },
          },
        ],
        responses: {
          '200': {
            description: 'Password changed successfully',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                data: { type: 'null' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
          '401': {
            description: 'Unauthorized - Invalid or missing token',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'error'],
            },
          },
        },
        consumes: ['application/json'],
        produces: ['application/json'],
      },
    },
    '/v1/reset-password': {
      post: {
        summary: 'Reset Password',
        deprecated: false,
        description:
          "This is a POST request to reset a user's password. The request requires a valid token and the new password in the request body. A successful POST request typically returns a `200 OK` or `204 No Content` response code.",
        tags: [],
        parameters: [
          {
            name: 'body',
            in: 'body',
            schema: {
              type: 'object',
              properties: {
                token: { type: 'string', description: 'Token for password reset' },
                newPassword: { type: 'string', description: 'New password for the user' },
              },
              required: ['token', 'newPassword'],
            },
          },
        ],
        responses: {
          '200': {
            description: 'Password reset successfully',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                data: { type: 'null' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
          '400': {
            description: 'Invalid or expired token',
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'error'],
            },
          },
        },
        consumes: ['application/json'],
        produces: ['application/json'],
      },
    },
    '/v1/auth/offering': {
      put: {
        summary: 'Submit offering Data',
        tags: ['Offering'],
        description: 'This is a POST request, submitting data to an API via the request body.',
        parameters: [
          {
            name: 'Authorization',
            in: 'header',
            required: true,
            type: 'string',
            description: 'Bearer token',
            example: 'Bearer <token>',
          },
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                oneOf: [
                  { $ref: '#/components/schemas/offeringStep1' },
                  { $ref: '#/components/schemas/offeringStep2' },
                  { $ref: '#/components/schemas/offeringStep3' },
                  { $ref: '#/components/schemas/offeringStep4' },
                  { $ref: '#/components/schemas/offeringStep5' },
                ],
              },
              examples: {
                offeringStep1: {
                  summary: 'Step 1 Data',
                  value: {
                    currentStep: 1,
                    overview: {
                      title: 'Real Estate Investment',
                      subTitle: 'Invest in prime locations',
                      description: 'This offering allows investors to participate in premium real estate assets.',
                      entityName: 'Real Estate Corp',
                      entityType: 'LLC',
                      webUrl: 'https://realestatecorp.com',
                      lineOfBusiness: 'Real Estate',
                      sourceOfFunds: 'Investor Funds',
                      location: 'New York, USA',
                      companyDescription: 'Real Estate Corp is a premier provider of real estate investments.',
                      icon: 'https://example.com/icon.png',
                      cover: 'https://example.com/cover.png',
                      logo: 'https://example.com/logo.png',
                    },
                  },
                },
                offeringStep2: {
                  summary: 'Step 2 Data',
                  value: {
                    offeringId: '66efba09a25ab8867b745939',
                    currentStep: 2,
                    projectDetails: {
                      assetType: 'Real Estate',
                      blockChainType: 'Ethereum',
                      offeringType: 'Equity',
                      tokenStandard: 'ERC-20',
                      offeringName: 'Real Estate Tokens',
                      CUSIP: '12345678',
                      authorizedCountries: ['USA', 'AAA', 'BBBB', 'CCCC'],
                      startDate: '2024-01-01T00:00:00Z',
                      endDate: '2024-12-31T23:59:59Z',
                      minInvestment: 10000,
                      maxInvestment: 500000,
                      assetName: 'Prime Real Estate Fund',
                      tokenTicker: 'REIT',
                      tokenSupply: 1000000,
                      tokenDecimals: 18,
                      holdTime: '2025-12-31T23:59:59Z',
                      maxTokenHolding: 100000,
                      projectYield: 8.5,
                      navLaunchPrice: 100,
                      latestNav: 105,
                      spvValuation: 100000000,
                      propertyType: 'Commercial',
                      propertySubtype: 'Fixed',
                      isTransferAgent: true,
                      taId: 'TA123456',
                      issuerId: 'ISS123456',
                      issuerWallet: '******************************************',
                      customFields: [
                        {
                          label: 'Field 1',
                          type: 'text',
                          value: 'Custom Value 1',
                        },
                      ],
                      isPrivate: true,
                      offeringMembers: ['<EMAIL>', '<EMAIL>'],
                    },
                  },
                },
                offeringStep3: {
                  summary: 'Step 3 Data',
                  value: {
                    offeringId: '66e94eb8536fee5891556744',
                    currentStep: 3,
                    documents: {
                      eSign: 'https://example.com/esign.pdf',
                      landRegistration: 'https://example.com/land_registration.pdf',
                      titleDocs: 'https://example.com/title_docs.pdf',
                      bankApproval: 'https://example.com/bank_approval.pdf',
                      encumbranceCertificate: 'https://example.com/encumbrance_certificate.pdf',
                      actualSite: 'https://example.com/actual_site.pdf',
                      propertyTaxReceipt: 'https://example.com/property_tax_receipt.pdf',
                      salesDeed: 'https://example.com/sales_deed.pdf',
                      powerOfAttorney: 'https://example.com/power_of_attorney.pdf',
                      resisteredSociety: 'https://example.com/registered_society.pdf',
                      tokenIcon: 'https://example.com/token_icon.png',
                      customDocs: [
                        {
                          docsLabel: 'Custom Document 1',
                          value: 'https://example.com/custom_doc1.pdf',
                        },
                      ],
                    },
                  },
                },
                offeringStep4: {
                  summary: 'Step 4 Data',
                  value: {
                    offeringId: '66e94eb8536fee5891556744',
                    currentStep: 4,
                    team: [
                      {
                        name: 'John Doe',
                        title: 'CEO',
                        summary: 'John has 20 years of experience in the real estate industry.',

                        url: 'https://realestatecorp.com/johndoe',
                        LinkedInUrl: 'https://linkedin.com/in/johndoe',
                      },
                      {
                        name: 'Jane Smith',
                        title: 'CFO',
                        summary: 'Jane has 15 years of experience in finance and real estate.',

                        url: 'https://realestatecorp.com/janesmith',
                        LinkedInUrl: 'https://linkedin.com/in/janesmith',
                      },
                    ],
                  },
                },
                offeringStep5: {
                  summary: 'Step 5 Data',
                  value: {
                    offeringId: '66ed28308d71a26a1fe4e322',
                    currentStep: 5,
                    isFinalSubmission: true,
                  },
                },
              },
            },
          },
        },
        responses: {
          '200': {
            $ref: '#/components/responses/offeringResponse',
          },
        },
        security: [
          {
            bearerAuth: [],
          },
        ],
      },
      get: {
        tags: ['Offering'],
        summary: 'Get List of Offerings',
        description: 'Fetch a list of all available offerings.',
        operationId: 'getOfferingList',
        parameters: [
          {
            name: 'page',
            in: 'query',
            required: false,
            description: 'Page number for pagination',
            schema: {
              type: 'integer',
              example: 1,
            },
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            description: 'Number of offerings to retrieve per page',
            schema: {
              type: 'integer',
              example: 10,
            },
          },
        ],
        responses: {
          '200': {
            description: 'Successful response with a list of offerings',
            content: {
              'application/json': {
                schema: {
                  type: 'array',
                  items: {
                    $ref: '#/components/schemas/offeringListItem',
                  },
                },
                example: [
                  {
                    offeringId: '66ed28308d71a26a1fe4e322',
                    title: 'Real Estate Investment',
                    description: 'Invest in prime real estate locations.',
                    tokenTicker: 'REIT',
                    startDate: '2024-01-01T00:00:00Z',
                    endDate: '2024-12-31T23:59:59Z',
                  },
                  {
                    offeringId: '74fcd54309d76b1f1ef3e123',
                    title: 'Tech Startup Investment',
                    description: 'Invest in the next big tech startup.',
                    tokenTicker: 'TECH',
                    startDate: '2024-02-01T00:00:00Z',
                    endDate: '2024-12-31T23:59:59Z',
                  },
                ],
              },
            },
          },
        },
      },
    },
    '/v1/auth/offering/{id}': {
      get: {
        tags: ['Offering'],
        summary: 'Get Offering Details by ID',
        description: 'Fetch the details of a specific offering by its ID.',
        operationId: 'getOfferingDetails',
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'Unique identifier of the offering',
            schema: {
              type: 'string',
              example: '66ed28308d71a26a1fe4e322',
            },
          },
        ],
        responses: {
          '200': {
            description: 'Successful response with offering details',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/offeringDetails',
                },
                example: {
                  message: 'Data Fetched Successfully',
                  status: 200,
                  data: {
                    _id: '66fb90b5a49f11b7c6648334',
                    overview: {
                      title: 'Real Estate Investment',
                      subTitle: 'Invest in prime locations',
                      description: 'This offering allows investors to participate in premium real estate assets.',
                      entityName: 'Real Estate Corp',
                      entityType: 'LLC',
                      webUrl: 'https://realestatecorp.com',
                      lineOfBusiness: 'Real Estate',
                      sourceOfFunds: 'Investor Funds',
                      location: 'New York, USA',
                      companyDescription: 'Real Estate Corp is a premier provider of real estate investments.',
                      icon: 'https://example.com/icon.png',
                      cover: 'https://example.com/cover.png',
                      logo: 'https://example.com/logo.png',
                    },
                    projectDetails: {
                      assetType: 'Real Estate',
                      blockChainType: 'Ethereum',
                      offeringType: 'Equity',
                      tokenStandard: 'ERC-20',
                      offeringName: 'Real Estate Tokens',
                      CUSIP: '12345678',
                      authorizedCountries: ['USA', 'AAA', 'BBBB', 'CCCC'],
                      startDate: '2024-01-01T00:00:00.000Z',
                      endDate: '2024-12-31T23:59:59.000Z',
                      minInvestment: 10000,
                      maxInvestment: 500000,
                      assetName: 'Prime Real Estate Fund',
                      tokenTicker: 'REIT',
                      tokenSupply: 1000000,
                      tokenDecimals: 18,
                      holdTime: '2025-12-31T23:59:59.000Z',
                      maxTokenHolding: 100000,
                      projectYield: 8.5,
                      navLaunchPrice: 100,
                      latestNav: 105,
                      spvValuation: 100000000,
                      propertyType: 'Commercial',
                      propertySubtype: 'Fixed',
                      isTransferAgent: true,
                      taId: 'TA123456',
                      issuerId: 'ISS123456',
                      issuerWallet: '******************************************',
                      isPrivate: true,
                      offeringMembers: ['<EMAIL>', '<EMAIL>'],
                      customFields: [
                        {
                          label: 'Field 1',
                          type: 'text',
                          value: 'Custom Value 1',
                        },
                      ],
                    },
                    documents: {
                      eSign: 'https://example.com/esign.pdf',
                      landRegistration: 'https://example.com/land_registration.pdf',
                      titleDocs: 'https://example.com/title_docs.pdf',
                      bankApproval: 'https://example.com/bank_approval.pdf',
                      encumbranceCertificate: 'https://example.com/encumbrance_certificate.pdf',
                      actualSite: 'https://example.com/actual_site.pdf',
                      propertyTaxReceipt: 'https://example.com/property_tax_receipt.pdf',
                      salesDeed: 'https://example.com/sales_deed.pdf',
                      powerOfAttorney: 'https://example.com/power_of_attorney.pdf',
                      resisteredSociety: 'https://example.com/registered_society.pdf',
                      tokenIcon: 'https://example.com/token_icon.png',
                      customDocs: [
                        {
                          docsLabel: 'Custom Document 1',
                          value: 'https://example.com/custom_doc1.pdf',
                        },
                      ],
                    },
                    team: [
                      {
                        name: 'Jane Smith',
                        title: 'CFO',
                        summary: 'Jane has 15 years of experience in finance and real estate.',
                        url: 'https://realestatecorp.com/janesmith',
                        LinkedInUrl: 'https://linkedin.com/in/janesmith',
                      },
                    ],
                    currentStep: 5,
                    isActive: true,
                    status: 'PENDING',
                  },
                  error: false,
                },
              },
            },
          },
          '404': {
            description: 'Offering not found',
          },
        },
      },
    },

    '/v1/auth/offering/manage-issuers': {
      get: {
        summary: 'Fetch list of issuers with their offerings',
        tags: ['Offering'],
        parameters: [
          {
            in: 'query',
            name: 'page',
            schema: {
              type: 'integer',
              default: 1,
            },
            description: 'The page number for pagination',
          },
          {
            in: 'query',
            name: 'limit',
            schema: {
              type: 'integer',
              default: 10,
            },
            description: 'The number of items per page',
          },
        ],
        responses: {
          '200': {
            description: 'Issuer List Fetched Successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: {
                      type: 'string',
                      example: 'Issuer List Fetched Successfully',
                    },
                    status: {
                      type: 'integer',
                      example: 200,
                    },
                    data: {
                      type: 'object',
                      properties: {
                        users: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              _id: {
                                type: 'string',
                                example: '66f7b081a134be42d6795401',
                                description: 'User ID',
                              },
                              email: {
                                type: 'string',
                                example: '<EMAIL>',
                                description: 'User email',
                              },
                              name: {
                                type: 'string',
                                example: 'hdfc',
                                description: 'User name',
                              },
                              userImage: {
                                type: 'string',
                                nullable: true,
                                example: 'https://example.com/user-image.png',
                                description: 'User profile image URL',
                              },
                              wallets: {
                                type: 'array',
                                items: {
                                  type: 'object',
                                  properties: {
                                    type: {
                                      type: 'string',
                                      example: 'MetamaskWallet',
                                    },
                                    address: {
                                      type: 'string',
                                      example: '******************************************',
                                    },
                                    isVerify: {
                                      type: 'boolean',
                                      example: false,
                                    },
                                    timestamp: {
                                      type: 'string',
                                      format: 'date-time',
                                      example: '2024-09-28T07:37:00.703Z',
                                    },
                                  },
                                },
                              },
                              offeringStatusCounts: {
                                type: 'object',
                                properties: {
                                  PENDING: {
                                    type: 'integer',
                                    example: 0,
                                  },
                                  REJECTED: {
                                    type: 'integer',
                                    example: 0,
                                  },
                                  APPROVED: {
                                    type: 'integer',
                                    example: 0,
                                  },
                                  RESUBMIT: {
                                    type: 'integer',
                                    example: 0,
                                  },
                                },
                              },
                            },
                          },
                        },
                        currentPage: {
                          type: 'integer',
                          example: 1,
                        },
                        totalPages: {
                          type: 'integer',
                          example: 1,
                        },
                        totalCount: {
                          type: 'integer',
                          example: 8,
                        },
                        nextPage: {
                          type: 'integer',
                          nullable: true,
                          example: null,
                        },
                        previousPage: {
                          type: 'integer',
                          nullable: true,
                          example: null,
                        },
                      },
                    },
                    error: {
                      type: 'boolean',
                      example: false,
                    },
                  },
                },
              },
            },
          },
          '400': {
            description: 'Bad Request',
          },
          '500': {
            description: 'Server Error',
          },
        },
      },
    },
  },

  swagger: '2.0',
  // host: "localhost:4000",
  schemes: ['http'],
  // basePath: "/",
  definitions: {},

  'x-components': {},
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
      },
    },
    schemas: {
      emptyRequest: {
        type: 'object',
        properties: {},
        required: [],
      },
      offeringStep1: {
        type: 'object',
        properties: {
          currentStep: {
            type: 'integer',
            example: 1,
          },
          overview: {
            type: 'object',
            properties: {
              title: { type: 'string' },
              subTitle: { type: 'string' },
              description: { type: 'string' },
              entityName: { type: 'string' },
              entityType: { type: 'string' },
              webUrl: { type: 'string' },
              lineOfBusiness: { type: 'string' },
              sourceOfFunds: { type: 'string' },
              location: { type: 'string' },
              companyDescription: { type: 'string' },
              icon: { type: 'string', format: 'url' },
              cover: { type: 'string', format: 'url' },
              logo: { type: 'string', format: 'url' },
            },
          },
        },
        example: {
          currentStep: 1,
          overview: {
            title: 'Real Estate Investment',
            subTitle: 'Invest in prime locations',
            description: 'This offering allows investors to participate in premium real estate assets.',
            entityName: 'Real Estate Corp',
            entityType: 'LLC',
            webUrl: 'https://realestatecorp.com',
            lineOfBusiness: 'Real Estate',
            sourceOfFunds: 'Investor Funds',
            location: 'New York, USA',
            companyDescription: 'Real Estate Corp is a premier provider of real estate investments.',
            icon: 'https://example.com/icon.png',
            cover: 'https://example.com/cover.png',
            logo: 'https://example.com/logo.png',
          },
        },
      },
      offeringStep2: {
        type: 'object',
        properties: {
          offeringId: { type: 'string' },
          currentStep: { type: 'integer', example: 2 },
          projectDetails: {
            type: 'object',
            properties: {
              assetType: { type: 'string' },
              blockChainType: { type: 'string' },
              offeringType: { type: 'string' },
              tokenStandard: { type: 'string' },
              offeringName: { type: 'string' },
              CUSIP: { type: 'string' },
              authorizedCountries: {
                type: 'array',
                items: { type: 'string' },
              },
              startDate: { type: 'string', format: 'date-time' },
              endDate: { type: 'string', format: 'date-time' },
              minInvestment: { type: 'integer' },
              maxInvestment: { type: 'integer' },
              assetName: { type: 'string' },
              tokenTicker: { type: 'string' },
              tokenSupply: { type: 'integer' },
              tokenDecimals: { type: 'integer' },
              holdTime: { type: 'string', format: 'date-time' },
              maxTokenHolding: { type: 'integer' },
              projectYield: { type: 'number', format: 'float' },
              navLaunchPrice: { type: 'number', format: 'float' },
              latestNav: { type: 'number', format: 'float' },
              spvValuation: { type: 'integer' },
              propertyType: { type: 'string' },
              propertySubtype: { type: 'string' },
              isTransferAgent: { type: 'boolean' },
              taId: { type: 'string' },
              issuerId: { type: 'string' },
              issuerWallet: { type: 'string' },
              customFields: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    label: { type: 'string' },
                    type: { type: 'string' },
                    value: { type: 'string' },
                  },
                },
              },
              isPrivate: { type: 'boolean' },
              offeringMembers: {
                type: 'array',
                items: { type: 'string' },
              },
            },
          },
        },
        example: {
          offeringId: '66efba09a25ab8867b745939',
          currentStep: 2,
          projectDetails: {
            assetType: 'Real Estate',
            blockChainType: 'Ethereum',
            offeringType: 'Equity',
            tokenStandard: 'ERC-20',
            offeringName: 'Real Estate Tokens',
            CUSIP: '12345678',
            authorizedCountries: ['USA', 'AAA', 'BBBB', 'CCCC'],
            startDate: '2024-01-01T00:00:00Z',
            endDate: '2024-12-31T23:59:59Z',
            minInvestment: 10000,
            maxInvestment: 500000,
            assetName: 'Prime Real Estate Fund',
            tokenTicker: 'REIT',
            tokenSupply: 1000000,
            tokenDecimals: 18,
            holdTime: '2025-12-31T23:59:59Z',
            maxTokenHolding: 100000,
            projectYield: 8.5,
            navLaunchPrice: 100,
            latestNav: 105,
            spvValuation: 100000000,
            propertyType: 'Commercial',
            propertySubtype: 'Fixed',
            isTransferAgent: true,
            taId: 'TA123456',
            issuerId: 'ISS123456',
            issuerWallet: '******************************************',
            customFields: [
              {
                label: 'Field 1',
                type: 'text',
                value: 'Custom Value 1',
              },
            ],
            isPrivate: true,
            offeringMembers: ['<EMAIL>', '<EMAIL>'],
          },
        },
      },
      offeringStep3: {
        type: 'object',
        properties: {
          offeringId: { type: 'string' },
          currentStep: { type: 'integer', example: 3 },
          documents: {
            type: 'object',
            properties: {
              eSign: { type: 'string', format: 'url' },
              landRegistration: { type: 'string', format: 'url' },
              titleDocs: { type: 'string', format: 'url' },
              bankApproval: { type: 'string', format: 'url' },
              encumbranceCertificate: { type: 'string', format: 'url' },
              actualSite: { type: 'string', format: 'url' },
              propertyTaxReceipt: { type: 'string', format: 'url' },
              salesDeed: { type: 'string', format: 'url' },
              powerOfAttorney: { type: 'string', format: 'url' },
              resisteredSociety: { type: 'string', format: 'url' },
              tokenIcon: { type: 'string', format: 'url' },
              customDocs: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    docsLabel: { type: 'string' },
                    value: { type: 'string', format: 'url' },
                  },
                },
              },
            },
          },
        },
        example: {
          offeringId: '66e94eb8536fee5891556744',
          currentStep: 3,
          documents: {
            eSign: 'https://example.com/esign.pdf',
            landRegistration: 'https://example.com/landreg.pdf',
            titleDocs: 'https://example.com/title.pdf',
            bankApproval: 'https://example.com/bankapproval.pdf',
            encumbranceCertificate: 'https://example.com/encumbcert.pdf',
            actualSite: 'https://example.com/actualsite.pdf',
            propertyTaxReceipt: 'https://example.com/taxreceipt.pdf',
            salesDeed: 'https://example.com/salesdeed.pdf',
            powerOfAttorney: 'https://example.com/poa.pdf',
            resisteredSociety: 'https://example.com/society.pdf',
            tokenIcon: 'https://example.com/token.png',
            customDocs: [
              {
                docsLabel: 'Other Document',
                value: 'https://example.com/customdoc.pdf',
              },
            ],
          },
        },
      },
      offeringStep4: {
        type: 'object',
        properties: {
          offeringId: { type: 'string' },
          currentStep: { type: 'integer', example: 4 },
          signers: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                name: { type: 'string' },
                email: { type: 'string', format: 'email' },
                designation: { type: 'string' },
                signature: { type: 'string', format: 'url' },
              },
            },
          },
        },
        example: {
          offeringId: '66eafed05397fced06543210',
          currentStep: 4,
          signers: [
            {
              name: 'John Doe',
              email: '<EMAIL>',
              designation: 'CEO',
              signature: 'https://example.com/johndoe-signature.png',
            },
          ],
        },
      },
      offeringStep5: {
        type: 'object',
        properties: {
          offeringId: { type: 'string' },
          currentStep: { type: 'integer', example: 5 },
          isFinalSubmission: { type: 'boolean' },
        },
        example: {
          offeringId: '66ed28308d71a26a1fe4e322',
          currentStep: 5,
          isFinalSubmission: true,
        },
      },
      offeringDetails: {
        type: 'object',
        properties: {
          overview: {
            type: 'object',
            properties: {
              title: { type: 'string', example: 'Fintech Innovation' },
              subTitle: {
                type: 'string',
                example: 'Pioneering digital payments',
              },
              description: {
                type: 'string',
                example: 'Leveraging blockchain for secure, transparent, and fast financial transactions.',
              },
              entityName: { type: 'string', example: 'FinTech Solutions' },
              entityType: { type: 'string', example: 'Private Company' },
              webUrl: { type: 'string', example: 'https://fintech.com' },
              lineOfBusiness: {
                type: 'string',
                example: 'Finance & Blockchain',
              },
              sourceOfFunds: { type: 'string', example: 'Private Investors' },
              location: { type: 'string', example: 'London, UK' },
              companyDescription: {
                type: 'string',
                example: 'A company focused on digital payment solutions using blockchain.',
              },
              icon: {
                type: 'string',
                example: 'https://example.com/icon3.png',
              },
              cover: {
                type: 'string',
                example: 'https://example.com/cover3.png',
              },
              logo: {
                type: 'string',
                example: 'https://example.com/logo3.png',
              },
            },
          },
          projectDetails: {
            type: 'object',
            properties: {
              assetType: { type: 'string', example: 'Finance' },
              blockChainType: { type: 'string', example: 'Ethereum' },
              offeringType: {
                type: 'string',
                example: 'Security Token Offering',
              },
              tokenStandard: { type: 'string', example: 'ERC-1400' },
              offeringName: { type: 'string', example: 'FinPay Token' },
              CUSIP: { type: 'string', example: '654987321' },
              authorizedCountries: {
                type: 'string',
                example: 'UK, France, Germany',
              },
              startDate: {
                type: 'string',
                format: 'date-time',
                example: '2024-03-01T00:00:00.000Z',
              },
              endDate: {
                type: 'string',
                format: 'date-time',
                example: '2024-12-01T00:00:00.000Z',
              },
              minInvestment: { type: 'integer', example: 10000 },
              maxInvestment: { type: 'integer', example: 500000 },
              assetName: { type: 'string', example: 'FinPay Payments' },
              tokenTicker: { type: 'string', example: 'FINP' },
              tokenSupply: { type: 'integer', example: 500000 },
              tokenDecimals: { type: 'integer', example: 18 },
              holdTime: {
                type: 'string',
                format: 'date-time',
                example: '2025-03-01T00:00:00.000Z',
              },
              maxTokenHolding: { type: 'integer', example: 50000 },
              projectYield: { type: 'number', example: 6.5 },
              navLaunchPrice: { type: 'number', example: 1.2 },
              latestNav: { type: 'number', example: 1.5 },
              spvValuation: { type: 'integer', example: 60000000 },
              propertyType: { type: 'string', example: 'Financial Services' },
              propertySubtype: { type: 'string', example: 'Fixed' },
              isTransferAgent: { type: 'boolean', example: true },
              ta_Id: { type: 'string', example: '64f9c8d3f001d21b4fc3b8a6' },
              issuer_Id: {
                type: 'string',
                example: '64f9c8d3f001d21b4fc3b8a7',
              },
              issuerWallet: {
                type: 'string',
                example: '******************************************',
              },
              customFields: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    _id: {
                      type: 'string',
                      example: '66e7dd40308c3df644bc55a6',
                    },
                    label: { type: 'string', example: 'Transaction Speed' },
                    type: { type: 'string', example: 'string' },
                    value: { type: 'string', example: 'Instant' },
                  },
                },
              },
            },
          },
          documents: {
            type: 'object',
            properties: {
              eSign: {
                type: 'string',
                example: 'https://example.com/docs/eSign3.pdf',
              },
              landRegistration: {
                type: 'string',
                example: 'https://example.com/docs/landRegistration3.pdf',
              },
              titleDocs: {
                type: 'string',
                example: 'https://example.com/docs/titleDocs3.pdf',
              },
              bankApproval: {
                type: 'string',
                example: 'https://example.com/docs/bankApproval3.pdf',
              },
              encumbranceCertificate: {
                type: 'string',
                example: 'https://example.com/docs/encumbranceCertificate3.pdf',
              },
              actualSite: {
                type: 'string',
                example: 'https://example.com/docs/actualSite3.pdf',
              },
              propertyTaxReceipt: {
                type: 'string',
                example: 'https://example.com/docs/propertyTaxReceipt3.pdf',
              },
              salesDeed: {
                type: 'string',
                example: 'https://example.com/docs/salesDeed3.pdf',
              },
              powerOfAttorney: {
                type: 'string',
                example: 'https://example.com/docs/powerOfAttorney3.pdf',
              },
              resisteredSociety: {
                type: 'string',
                example: 'https://example.com/docs/resisteredSociety3.pdf',
              },
              tokenIcon: {
                type: 'string',
                example: 'https://example.com/docs/tokenIcon3.pdf',
              },
              customDocs: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    _id: {
                      type: 'string',
                      example: '66e7dd40308c3df644bc55a7',
                    },
                    docsLabel: {
                      type: 'string',
                      example: 'Transaction Details',
                    },
                    value: {
                      type: 'string',
                      example: 'https://example.com/docs/transactionDetails3.pdf',
                    },
                  },
                },
              },
            },
          },
          _id: { type: 'string', example: '66e7d3787fb0f8c4cb3e8dce' },
          userId: { type: 'string', example: '64f9b012f005d21a45c1a8a5' },
          team: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                teamIcon: {
                  type: 'string',
                  example: 'https://example.com/teamIcon3.png',
                },
                teamDescription: {
                  type: 'string',
                  example: 'John Doe, experienced FinTech advisor',
                },
              },
            },
          },
        },
      },
      offeringListItem: {
        type: 'object',
        properties: {
          message: {
            type: 'string',
            example: 'Users fetched successfully.',
          },
          status: {
            type: 'integer',
            example: 200,
          },
          data: {
            type: 'object',
            properties: {
              offering: {
                type: 'array',
                items: {
                  $ref: '#/components/schemas/offeringDetails',
                },
              },
              currentPage: {
                type: 'integer',
                example: 1,
              },
              totalPages: {
                type: 'integer',
                example: 1,
              },
              totalCount: {
                type: 'integer',
                example: 10,
              },
              nextPage: {
                type: 'string',
                nullable: true,
                example: null,
              },
              previousPage: {
                type: 'string',
                nullable: true,
                example: null,
              },
            },
          },
          error: {
            type: 'boolean',
            example: false,
          },
        },
      },
    },
    responses: {
      CreatedResponse: {
        description: 'Created',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  example: 'Congratulations! You Have Successfully Registered. Please Verify Your Email To Continue Enjoying Our Services.',
                },
                status: { type: 'integer', example: 201 },
                data: {
                  type: 'object',
                  properties: {
                    isOtpActive: { type: 'boolean', example: true },
                  },
                  required: ['isOtpActive'],
                },
                error: { type: 'boolean', example: false },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
        },
      },
      NotFoundResponse: {
        description: 'Not Found',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Page Not Found' },
                status: { type: 'integer', example: 404 },
                data: { type: 'object', example: {} },
                error: { type: 'boolean', example: true },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
        },
      },
      UnauthorizedResponse: {
        description: 'Unauthorized',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                data: { type: 'object' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
        },
      },
      GenericSuccessResponse: {
        description: 'Successful Operation',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                data: { type: 'object' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
        },
      },
      ServerErrorResponse: {
        description: 'Internal Server Error',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer' },
                data: { type: 'object' },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
        },
      },
      offeringResponse: {
        description: 'Created',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                },
                error: { type: 'boolean' },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
        },
      },
    },
  },
};
