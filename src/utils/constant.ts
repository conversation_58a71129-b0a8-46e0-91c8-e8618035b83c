export const otpLength = 6;
export const passwordPattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
export const namePattern = /^[A-Za-z\s]+$/;
export const otpPattern = /^[0-9]{6}$/;
export const maxPasswordHistory = 5;
export const offeringDocs = [
  { title: 'E-Signature document', name: 'eSign' },
  { title: 'Pitch Deck', name: 'pitchDeck' },
  {
    title: 'Confidential Information Memorendum',
    name: 'confidentialInformationMemorendum',
  },
  { title: 'Land Registration', name: 'landRegistration' },
  { title: 'Title document', name: 'titleDocs' },
  { title: 'Bank approval', name: 'bankApproval' },
  { title: 'Encumbrance certificate', name: 'encumbranceCertificate' },
  { title: 'Property tax receipt', name: 'propertyTaxReceipt' },
  { title: 'Article Of Association', name: 'articlesOfAssociation' },
  { title: 'Operating Agreement', name: 'operatingAgreement' },
  { title: 'Tax Assignement Letter', name: 'taxAssignmentLetter' },
  { title: 'Certificate Of Registration', name: 'certificateOfRegistration' },
  { title: 'Register Of Managers', name: 'registerOfManagers' },
];

export const coolDownTimeInSeconds = 60;
export const nameMaxLength = 70;
export const nameMinLength = 3;
export const emailMaxLength = 200;
export const passwordMaxLength = 50;
export const passwordMinLength = 2;
export const alphanumericPattern = /^[A-Za-z0-9À-ÖØ-öø-ÿ\s'’\-]+$/;
export const maxKycAttempt = 3;
export const urlPattern = /^(https?:\/\/)?([a-zA-Z0-9-]+\.)?[a-zA-Z0-9-]{1,}\.[a-zA-Z]{2,}(\/.*)?$/;
