import * as Joi from 'joi';
import {
  AssetTypeEnum,
  BlockchainEnum,
  DocumentTypesEnum,
  EntityTypeEnum,
  IncomeSourceEnum,
  offeringDocumentTypesEnum,
  offeringStatusEnum,
  OfferingTypeEnum,
  orderStatusEnum,
  PropertyTypeEnum,
  StandardTokenEnum,
} from '../utils/common.interface';
import { alphanumericPattern, emailMaxLength, nameMaxLength, nameMinLength, namePattern, urlPattern } from '../utils/constant';
import CommonHelper from './common.helper';

export const options = {
  errors: {
    wrap: {
      label: '',
    },
  },
};

export const capitalize = (s: string) => {
  return s && s[0].toUpperCase() + s.slice(1);
};

const offeringStatusValues = Object.values(offeringStatusEnum);
const orderValues = Object.values(orderStatusEnum);
const entityType = Object.values(EntityTypeEnum);
const incomeSource = Object.values(IncomeSourceEnum);
const assetType = Object.values(AssetTypeEnum);
const chainType = Object.values(BlockchainEnum);
const offeringType = Object.values(OfferingTypeEnum);
const standardToken = Object.values(StandardTokenEnum);
const propertyType = Object.values(PropertyTypeEnum);
const offeringDocumentTypes = Object.values(offeringDocumentTypesEnum);
const docsTypeValues = Object.values(DocumentTypesEnum);

export const paginationSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1).required(),
  limit: Joi.number().integer().min(1).default(10).required(),
  sort: Joi.object().pattern(Joi.string(), Joi.number().valid(1, -1)).default({ createdAt: -1 }).optional(),
  search: Joi.string().allow('').optional(),
});

export const offeringStatusSchema = Joi.string()
  .trim()
  .valid(...offeringStatusValues)
  .optional()
  .label('Status')
  .messages({
    'string.empty': '{#label} cannot be empty',
    'any.only': `{#label} must be one of the following: ${offeringStatusValues.join(', ')}`,
    'any.required': '{#label} is required',
  });
export const transactionsTypeSchema = Joi.string()
  .trim()
  .valid(...orderValues)
  .optional()
  .label('Type')
  .messages({
    'string.empty': '{#label} cannot be empty',
    'any.only': `{#label} must be one of the following: ${orderValues.join(', ')}`,
    'any.required': '{#label} is required',
  });

export const currentStepSchema = Joi.object({
  currentStep: Joi.number().required().label('Current Step'),
});

export const overviewSchema = Joi.object({
  title: Joi.string()
    .trim()
    .required()
    .label('Title')
    .min(2)
    .max(100)
    // .pattern(alphanumericPattern)
    .messages({
      'string.empty': '{#label} cannot be empty',
      'any.required': '{#label} is required',
      'string.min': '{#label} must be at least {#limit} characters long',
      'string.max': '{#label} must be at most {#limit} characters long',
      'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces',
    }),
  subTitle: Joi.string()
    .trim()
    .required()
    .label('Sub Title')
    .min(2)
    .max(200)
    // .pattern(alphanumericPattern)
    .messages({
      'string.empty': '{#label} cannot be empty',
      'any.required': '{#label} is required',
      'string.min': '{#label} must be at least {#limit} characters long',
      'string.max': '{#label} must be at most {#limit} characters long',
      'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces',
    }),
  description: Joi.string()
    .trim()
    .required()
    .label('Description')
    .min(2)
    .max(500)
    // .pattern(alphanumericPattern)
    .messages({
      'string.empty': '{#label} cannot be empty',
      'any.required': '{#label} is required',
      'string.min': '{#label} must be at least {#limit} characters long',
      'string.max': '{#label} must be at most {#limit} characters long',
      'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces',
    }),
  entityName: Joi.string().trim().required().label('Full Legal Entity Name').min(2).max(500).pattern(alphanumericPattern).messages({
    'string.empty': '{#label} cannot be empty',
    'any.required': '{#label} is required',
    'string.min': '{#label} must be at least {#limit} characters long',
    'string.max': '{#label} must be at most {#limit} characters long',
    'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces',
  }),
  entityType: Joi.string()
    .trim()
    .valid(...entityType)
    .required()
    .label('Entity Type')
    .messages({
      'string.empty': '{#label} cannot be empty',
      'any.only': `{#label} must be one of the following: ${entityType.join(', ')}`,
      'any.required': '{#label} is required',
    }),
  webUrl: Joi.string().optional().pattern(urlPattern).label('Website').messages({
    'string.empty': '{#label}  must be a valid URL',
    'string.pattern.base': '{#label} must be a valid URL',
  }),
  lineOfBusiness: Joi.string().trim().required().label('Line of Business').min(2).max(200).pattern(alphanumericPattern).messages({
    'string.empty': '{#label} cannot be empty',
    'any.required': '{#label} is required',
    'string.min': '{#label} must be at least {#limit} characters long',
    'string.max': '{#label} must be at most {#limit} characters long',
    'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces',
  }),
  sourceOfFunds: Joi.string()
    .trim()
    .valid(...incomeSource)
    .required()
    .label('Source of Funds')
    .messages({
      'string.empty': '{#label} cannot be empty',
      'any.only': `{#label} must be one of the following: ${incomeSource.join(', ')}`,
      'any.required': '{#label} is required',
    }),
  location: Joi.string().required().min(2).max(200).label('Location').messages({
    'string.empty': '{#label} cannot be empty',
    'any.required': '{#label} is required',
    'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces',
  }),
  companyDescription: Joi.string()
    .trim()
    .required()
    .label('Company Description')
    .min(2)
    .max(500)
    // .pattern(alphanumericPattern)
    .messages({
      'string.empty': '{#label} cannot be empty',
      'any.required': '{#label} is required',
      'string.min': '{#label} must be at least {#limit} characters long',
      'string.max': '{#label} must be at most {#limit} characters long',
      'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces',
    }),
  icon: Joi.string().optional().label('icon').messages({
    'string.empty': '{#label} cannot be empty',
    'any.required': '{#label} is required',
    'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces',
  }),
  cover: Joi.string().optional().label('cover').messages({
    'string.empty': '{#label} cannot be empty',
    'any.required': '{#label} is required',
    'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces',
  }),
  logo: Joi.string().required().label('logo').messages({
    'string.empty': '{#label} cannot be empty',
    'any.required': '{#label} is required',
    'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces',
  }),
});
export const normalizeDate = (date: string | Date) => {
  const normalized = new Date(date);
  normalized.setHours(0, 0, 0, 0); // Set time to midnight
  return normalized;
};
export const projectDetailsSchema = Joi.object({
  assetType: Joi.string()
    .trim()
    .valid(...assetType)
    .required()
    .label('Asset Type')
    .messages({
      'string.empty': '{#label} cannot be empty',
      'any.only': `{#label} must be one of the following: ${assetType.join(', ')}`,
      'any.required': '{#label} is required',
    }),
  blockChainType: Joi.string()
    .trim()
    .valid(...chainType)
    .required()
    .label('Blockchain Type')
    .messages({
      'string.empty': '{#label} cannot be empty',
      'any.only': `{#label} must be one of the following: ${chainType.join(', ')}`,
      'any.required': '{#label} is required',
    }),
  offeringType: Joi.string()
    .trim()
    .valid(...offeringType)
    .required()
    .label('Offering Type')
    .messages({
      'string.empty': '{#label} cannot be empty',
      'any.only': `{#label} must be one of the following: ${offeringType.join(', ')}`,
      'any.required': '{#label} is required',
    }),
  tokenStandard: Joi.string()
    .trim()
    .valid(...standardToken)
    .required()
    .label('Token Standard')
    .messages({
      'string.empty': '{#label} cannot be empty',
      'any.only': `{#label} must be one of the following: ${standardToken.join(', ')}`,
      'any.required': '{#label} is required',
    }),
  offeringName: Joi.string()
    .trim()
    .required()
    .min(nameMinLength)
    .max(100)
    // .pattern(alphanumericPattern)
    .label('Offering Name')
    .messages({
      'string.empty': '{#label} cannot be empty',
      'any.required': '{#label} is required',
      'string.min': `{#label} must contain at least ${nameMinLength} characters.`,
      'string.max': `{#label} must be at most 100 characters long.`,
      'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces',
    }),
  CUSIP: Joi.string()
    .trim()
    .required()
    .min(nameMinLength)
    .max(nameMaxLength)
    .pattern(alphanumericPattern)
    .label('CUSIP')
    .messages({
      'string.empty': '{#label} cannot be empty',
      'any.required': '{#label} is required',
      'string.min': `{#label} must contain at least ${nameMinLength} characters.`,
      'string.max': `{#label} must be at most ${nameMaxLength} characters long.`,
      'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces',
    }),

  isAuthorized: Joi.boolean().required().label('Authorized').messages({
    'any.required': '{#label} is required',
  }),
  authorizedCountries: Joi.array()
    .items(
      Joi.object({
        name: Joi.string().required().label('Country Name'),
        isoCode: Joi.string().required().label('ISO Code'),
        countryCode: Joi.string().required().label('Country Code'),
      }),
    )
    .min(1)
    .label('Countries')
    .required()
    .messages({
      'array.base': '{#label} must be an array',
      'array.min': '{#label} must contain at least one country',
      'string.base': 'Each Countries must be a valid string',
      'string.empty': '{#label} cannot be empty',
      'object.base': '{#label} must contain valid objects',
      'any.required': '{#label} is required',
    }),
  startDate: Joi.date()
    .optional()
    .label('Start Date')
    .custom((value, helpers) => {
      const currentDate = normalizeDate(new Date());
      const inputDate = normalizeDate(value);

      if (inputDate < currentDate) {
        return helpers.error('date.min', {
          message: `Start Date must be greater than or equal to today's date`, // Set the message directly here
        });
      }
      return value; // Return original value if no error
    })
    .messages({
      'any.required': '{#label} is required',
      'date.base': '{#label} must be a valid date',
      'string.min': '{#label} must be greater than or equal to today date',
      'string.CustomError': '{#label} must be greater than or equal to today date',
    }),

  endDate: Joi.date().greater(Joi.ref('startDate')).optional().label('End Date').messages({
    'any.required': '{#label} is required',
    'date.base': '{#label} must be a valid date',
    'date.greater': '{#label} must be greater than the Start Date',
  }),

  tokenSupply: Joi.number().required().min(0).label('Token Supply').messages({
    'any.required': '{#label} is required',
    'number.min': '{#label} must be a positive number',
  }),

  minInvestment: Joi.number().required().min(0).label('Minimum Investment').messages({
    'any.required': '{#label} is required',
    'number.min': '{#label} must be at least {#limit}',
  }),

  maxInvestment: Joi.number().required().min(Joi.ref('minInvestment')).label('Maximum Investment').messages({
    'any.required': '{#label} is required',
    'number.min': '{#label} must be at least {#limit}, which is equal to or greater than the Minimum Investment',
  }),
  assetName: Joi.string()
    .trim()
    .required()
    .min(nameMinLength)
    .max(nameMaxLength)
    // .pattern(alphanumericPattern)
    .label('Asset Name')
    .messages({
      'string.empty': '{#label} cannot be empty',
      'any.required': '{#label} is required',
      'string.min': `{#label} must contain at least ${nameMinLength} characters.`,
      'string.max': `{#label} must be at most ${nameMaxLength} characters long.`,
      'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces',
    }),
  tokenTicker: Joi.string().trim().required().min(1).max(11).pattern(alphanumericPattern).label('Token Ticker').messages({
    'string.empty': '{#label} cannot be empty',
    'any.required': '{#label} is required',
    'string.min': `{#label} must contain at least 1 characters.`,
    'string.max': `{#label} must be at most 11 characters long.`,
    'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces',
  }),

  tokenDecimals: Joi.number().required().min(0).max(18).label('Token Decimals').messages({
    'any.required': '{#label} is required',
    'number.min': '{#label} must be a positive number',
    'number.max': 'Please enter a {#label} that is 18 or less.',
  }),
  lockupMonths: Joi.number().required().min(0).max(100).label('Lockup Months').messages({
    'any.required': '{#label} is required',
    'number.base': '{#label} must be a number',
    'number.min': '{#label} must be a positive number',
  }),
  holdTime: Joi.date().required().label('Hold Time').messages({
    'any.required': '{#label} is required',
    'date.base': '{#label} must be a valid date',
  }),
  maxTokenHolding: Joi.number().required().min(0).label('Maximum Token Holding').messages({
    'any.required': '{#label} is required',
    'number.min': '{#label} must be a positive number',
  }),

  // navLaunchPrice: Joi.number().required().min(0).label('NAV Launch Price').messages({
  //   'any.required': '{#label} is required',
  //   'number.min': '{#label} must be a positive number',
  // }),

  navLaunchPrice: Joi.when('assetType', {
    is: AssetTypeEnum.RealEstate,
    then: Joi.number()
      .required()
      .min(0)
      // .max(100)
      .label('NAV Launch Price')
      .messages({
        'any.required': '{#label} is required',
        'number.min': '{#label} must be a positive number',
      }),
    otherwise: Joi.forbidden(),
  }),

  latestNav: Joi.when('assetType', {
    is: AssetTypeEnum.RealEstate,
    then: Joi.number().required().unsafe().min(0).max(999999999999999).label('Latest NAV').messages({
      'any.required': '{#label} is required',
      'number.min': '{#label} must be a positive number',
      'number.max': '{#label} cannot exceed 15 digits.',
      'number.base': '{#label} must be a number',
    }),
    otherwise: Joi.forbidden(),
  }),
  // aumSize: Joi.number().required().min(0).label("AUM Size").messages({
  //     "any.required": "{#label} is required",
  //     "number.min": "{#label} must be a positive number",
  // }),
  // spvValuation: Joi.number().required().min(0).label("SPV Valuation").messages({
  //     "any.required": "{#label} is required",
  //     "number.min": "{#label} must be a positive number",
  // }),
  propertyType: Joi.when('assetType', {
    is: AssetTypeEnum.RealEstate,
    then: Joi.string()
      .trim()
      .valid(...propertyType)
      .required()
      .label('Property Type')
      .messages({
        'string.empty': '{#label} cannot be empty',
        'any.only': `{#label} must be one of the following: ${propertyType.join(', ')}`,
        'any.required': '{#label} is required',
      }),
    otherwise: Joi.forbidden(),
  }),

  propertySubtype: Joi.when('assetType', {
    is: AssetTypeEnum.RealEstate,
    then: Joi.string()
      .trim()
      .optional()
      .min(2)
      .max(200)
      // .pattern(alphanumericPattern)
      .label('Property Sub Type')
      .messages({
        'string.empty': '{#label} is not allowed empty',
        'any.required': '{#label} is required',
        'string.min': `{#label} must contain at least 2 characters.`,
        'string.max': `{#label} must be at most 200 characters long.`,
        'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces',
      }),
    otherwise: Joi.forbidden(),
  }),

  yearBuilt: Joi.when('assetType', {
    is: AssetTypeEnum.RealEstate,
    then: Joi.number().required().min(1).max(9999).label('Year Built').messages({
      'any.required': '{#label} is required',
      'number.min': '{#label} must be a positive number',
    }),
    otherwise: Joi.forbidden(),
  }),

  lotSize: Joi.when('assetType', {
    is: AssetTypeEnum.RealEstate,
    then: Joi.number()
      .unsafe()
      .required()
      .min(1)
      .max(999999999999999)
      .label('lotSize')
      .messages({
        'any.required': '{#label} is required',
        'number.min': '{#label} must be a positive number',
        'number.max': '{#label} cannot exceed 15 digits.',
        'number.base': '{#label} must be a number',
      })
      .custom((value, helpers) => {
        if (value.toString().length > 15) {
          return helpers.error('number.max');
        }
        return value;
      }),

    otherwise: Joi.forbidden(),
  }),
  occupancy: Joi.when('assetType', {
    is: AssetTypeEnum.RealEstate,
    then: Joi.number().required().min(0).max(100).label('occupancy').messages({
      'any.required': '{#label} is required',
      'number.min': '{#label} must be a positive number',
    }),
    otherwise: Joi.forbidden(),
  }),

  // yeldType: Joi.string()
  //     .trim()
  //     .required()
  //     .min(nameMinLength)
  //     .max(nameMaxLength)
  //     .pattern(alphanumericPattern)
  //     .label("Yield Type")
  //     .messages({
  //         "string.empty": "{#label} is required",
  //         "any.required": "{#label} is required",
  //         "string.min": `{#label} must contain at least ${nameMinLength} characters.`,
  //         "string.max": `{#label} must be at most ${nameMaxLength} characters long.`,
  //         "string.pattern.base":
  //             "{#label} must contain only alphanumeric characters and spaces",
  //     }),
  isTransferAgent: Joi.boolean().required().label('Transfer Agent').messages({
    'any.required': '{#label} is required',
  }),
  taId: Joi.string()
    .trim()
    .label('Transfer Agent')
    .when('isTransferAgent', {
      is: true,
      then: Joi.string()
        .required()
        .messages({
          'string.empty': '{#label} cannot be empty',
          'any.required': 'Please Select {#label}',
          'string.min': `{#label} must contain at least ${nameMinLength} characters.`,
          'string.max': `{#label} must be at most ${nameMaxLength} characters long.`,
          'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces',
        }),
      otherwise: Joi.optional(),
    }),
  issuerId: Joi.string()
    .trim()
    .required()
    .label('Issuer ID')
    .messages({
      'string.empty': '{#label} cannot be empty',
      'any.required': '{#label} is required',
      'string.min': `{#label} must contain at least ${nameMinLength} characters.`,
      'string.max': `{#label} must be at most ${nameMaxLength} characters long.`,
      'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces',
    }),
  issuerWallet: Joi.string().trim().required().label('Issuer Wallet').messages({
    'string.empty': '{#label} cannot be empty',
    'any.required': '{#label} is required',
  }),
  customFields: Joi.array()
    .items(
      Joi.object({
        label: Joi.string().trim().required().min(2).max(100).required().label('Custom Field Label').messages({
          'any.required': '{#label} is required',
        }),
        type: Joi.string().trim().required().min(2).max(100).label('Custom Field Type').messages({
          'any.required': '{#label} is required',
        }),
        value: Joi.any().required().label('Custom Field Value').messages({
          'any.required': '{#label} is required',
        }),
      }),
    )
    .optional(),
  isPrivate: Joi.boolean().required(),
  offeringMembers: Joi.array()
    .items(Joi.string().email().optional())
    .when('isPrivate', {
      is: true, // If isPrivate is true
      then: Joi.array()
        .items(Joi.string().email())
        .min(1) // Ensure at least one email is present
        .required()
        .label('Offering members')
        .default([])
        .messages({
          'array.base': '{#label} must be an array',
          'string.base': 'Each member must be a valid string',
          'string.email': 'Please send a valid email in {#label}',
          'any.required': 'Offering members are required when the offering is private',
          'array.min': 'At least one email is required in {#label}', // Custom message for minimum requirement
        }),
      otherwise: Joi.optional(),
    }),

  projectedYield: Joi.number().required().min(0).max(100).label('Projected Yieldzzzzzzzzzzzzzzzzzzzzzzzz').messages({
    'any.required': '{#label} is required',
    'number.min': '{#label} must be a positive number',
  }),

  launchValuation: Joi.when('assetType', {
    is: AssetTypeEnum.Equity,
    then: Joi.number()
      .required()
      .min(0)
      // .max(100)
      .label('Launch Valuation')
      .messages({
        'any.required': '{#label} is required',
        'number.min': '{#label} must be a positive number',
      }),
    otherwise: Joi.forbidden(),
  }),

  previousValuation: Joi.when('assetType', {
    is: AssetTypeEnum.Equity,
    then: Joi.number()
      .required()
      .min(0)
      // .max(100)
      .label('Previous Valuation')
      .messages({
        'any.required': '{#label} is required',
        'number.min': '{#label} must be a positive number',
      }),
    otherwise: Joi.forbidden(),
  }),

  deRatio: Joi.when('assetType', {
    is: AssetTypeEnum.Equity,
    then: Joi.number().required().min(0).max(100).label('D/E Ratio').messages({
      'any.required': '{#label} is required',
      'number.min': '{#label} must be a positive number',
    }),
    otherwise: Joi.forbidden(),
  }),
}).custom((value, helpers) => {
  // Custom validation to check that tokenSupply >= maxInvestment
  // if (value.tokenSupply < value.maxInvestment) {
  //   return helpers.error('any.custom', {
  //     message: 'Token Supply must be greater than or equal to Maximum Investment',
  //   });
  // }
  if (value?.startDate) {
    const currentDate = normalizeDate(new Date());
    const inputDate = normalizeDate(value?.startDate);

    if (inputDate < currentDate) {
      return helpers.error('any.custom', {
        message: `Start Date must be greater than or equal to today's date`,
      });
    }
  }
  return value;
});

export const nameSchema: Joi.StringSchema = Joi.string()
  .trim()
  .min(nameMinLength)
  .max(nameMaxLength)
  .required()
  .pattern(namePattern)
  .label('Name')
  .messages({
    'string.empty': '{#label} cannot be empty',
    'any.required': '{#label} is required',
    'string.min': `{#label} must contain at least ${nameMinLength} characters.`,
    'string.max': `{#label} must be at ${nameMaxLength} characters long`,
    'string.pattern.base': '{#label} must contain only alphabetic characters and spaces',
  });

export const emailSchema = Joi.string()
  .trim()
  .max(emailMaxLength)
  .email()
  .required()
  .lowercase()
  .label('Email')
  .messages({
    'string.empty': '{#label} cannot be empty',
    'any.required': '{#label} is required',
    'string.max': `{#label} must be at most ${emailMaxLength} characters long`,
  });
export const teamSchema = Joi.array().items(
  Joi.object({
    name: nameSchema,
    title: Joi.string()
      .required()
      .min(2)
      .max(100)
      .required()
      // .pattern(alphanumericPattern)
      .label('Team Member Title')
      .messages({
        'any.required': '{#label} is required',
      }),
    summary: Joi.string().optional().min(2).max(100).messages({
      'string.empty': '{#label} is not allowed',
      'any.required': '{#label} is required',
      'string.min': '{#label} must be at least {#limit} characters long',
      'string.max': '{#label} must be at most {#limit} characters long',
      'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces',
    }),
    // email: emailSchema,
    url: Joi.string().optional().pattern(urlPattern).label('Website').messages({
      'string.pattern.base': '{#label} must be a valid URL',
    }),
    linkedInUrl: Joi.string().optional().label('LinkedIn URL').messages({
      'string.uri': '{#label} must be a valid URL',
    }),
    twitterUrl: Joi.string().optional().label('Twitter URL').messages({
      'string.uri': '{#label} must be a valid URL',
    }),
    key: Joi.string().required().min(2).max(100).optional().label('Key').messages({
      'any.required': '{#label} is required',
    }),
  }),
);

export const offeringDocumentsSchema = Joi.object({
  assetType: Joi.string()
    .trim()
    .valid(...assetType)
    .optional()
    .label('Asset Type')
    .messages({
      'string.empty': '{#label} cannot be empty',
      'any.only': `{#label} must be one of the following: ${assetType.join(', ')}`,
      'any.required': '{#label} is required',
    }),

  // Common documents (conditionally required based on assetType)
  eSign: Joi.string()
    .uri()
    .when('assetType', {
      is: Joi.exist(),
      then: Joi.required(),
      otherwise: Joi.optional(),
    })
    .label('E-Signature Document')
    .messages({
      'string.uri': '{#label} must be a valid URL',
      'any.required': '{#label} is required when assetType is provided',
    }),

  pitchDeck: Joi.string()
    .uri()
    .when('assetType', {
      is: Joi.exist(),
      then: Joi.required(),
      otherwise: Joi.optional(),
    })
    .label('Pitch Desk')
    .messages({
      'string.uri': '{#label} must be a valid URL',
      'any.required': '{#label} is required when assetType is provided',
    }),

  confidentialInformationMemorendum: Joi.string()
    .uri()
    .when('assetType', {
      is: Joi.exist(),
      then: Joi.required(),
      otherwise: Joi.optional(),
    })
    .label('Confidential Information Memorandum')
    .messages({
      'string.uri': '{#label} must be a valid URL',
      'any.required': '{#label} is required when assetType is provided',
    }),

  // Real Estate document

  landRegistration: Joi.string()
    .uri()
    .when('assetType', {
      is: AssetTypeEnum.RealEstate,
      then: Joi.required(),
      otherwise: Joi.forbidden(),
    })
    .label('Land Registration Document')
    .messages({
      'string.uri': '{#label} must be a valid URL',
      'any.required': '{#label} is required',
    }),

  titleDocs: Joi.string()
    .uri()
    .when('assetType', {
      is: AssetTypeEnum.RealEstate,
      then: Joi.required(),
      otherwise: Joi.forbidden(),
    })
    .label('Title Document')
    .messages({
      'string.uri': '{#label} must be a valid URL',
      'any.required': '{#label} is required',
    }),

  bankApproval: Joi.string()
    .uri()
    .when('assetType', {
      is: AssetTypeEnum.RealEstate,
      then: Joi.required(),
      otherwise: Joi.forbidden(),
    })
    .label('Bank Approval Document')
    .messages({
      'string.uri': '{#label} must be a valid URL',
      'any.required': '{#label} is required',
    }),

  encumbranceCertificate: Joi.string()
    .uri()
    .when('assetType', {
      is: AssetTypeEnum.RealEstate,
      then: Joi.required(),
      otherwise: Joi.forbidden(),
    })
    .label('Encumbrance Certificate')
    .messages({
      'string.uri': '{#label} must be a valid URL',
      'any.required': '{#label} is required',
    }),
  propertyTaxReceipt: Joi.string()
    .uri()
    .when('assetType', {
      is: AssetTypeEnum.RealEstate,
      then: Joi.required(),
      otherwise: Joi.forbidden(),
    })
    .label('Property Tax Receipt')
    .messages({
      'string.uri': '{#label} must be a valid URL',
      'any.required': '{#label} is required',
    }),

  // Equity Document

  articlesOfAssociation: Joi.string()
    .uri()
    .when('assetType', {
      is: AssetTypeEnum.Equity,
      then: Joi.required(),
      otherwise: Joi.forbidden(),
    })
    .label('Articles of Association')
    .messages({
      'string.uri': '{#label} must be a valid URL',
    }),

  operatingAgreement: Joi.string()
    .uri()
    .when('assetType', {
      is: AssetTypeEnum.Equity,
      then: Joi.required(),
      otherwise: Joi.forbidden(),
    })
    .label('Operating Agreement')
    .messages({
      'string.uri': '{#label} must be a valid URL',
      'any.required': '{#label} is required',
    }),

  taxAssignmentLetter: Joi.string()
    .uri()
    .when('assetType', {
      is: AssetTypeEnum.Equity,
      then: Joi.required(),
      otherwise: Joi.forbidden(),
    })
    .label('Tax Assignment Letter')
    .messages({
      'string.uri': '{#label} must be a valid URL',
      'any.required': '{#label} is required',
    }),

  certificateOfRegistration: Joi.string()
    .uri()
    .when('assetType', {
      is: AssetTypeEnum.Equity,
      then: Joi.required(),
      otherwise: Joi.forbidden(),
    })
    .label('Certificate Of Registration')
    .messages({
      'string.uri': '{#label} must be a valid URL',
      'any.required': '{#label} is required',
    }),
  registerOfManagers: Joi.string()
    .uri()
    .when('assetType', {
      is: AssetTypeEnum.Equity,
      then: Joi.required(),
      otherwise: Joi.forbidden(),
    })
    .label('Register of Managers')
    .messages({
      'string.uri': '{#label} must be a valid URL',
      'any.required': '{#label} is required',
    }),

  customDocs: Joi.array()
    .items(
      Joi.object({
        docsLabel: Joi.string().required().label('Custom Document Label'),
        value: Joi.string().uri().required().label('Custom Document URL').messages({
          'string.uri': '{#label} must be a valid URL',
          'any.required': '{#label} is required',
        }),
      }),
    )
    .optional(),
});

export const offeringDocsTypeSchema = Joi.string()
  .trim()
  .valid(...offeringDocumentTypes)
  .custom((value, helpers) => {
    if (value === offeringDocumentTypesEnum.customImage || value === offeringDocumentTypesEnum.url) {
      const randomKey = CommonHelper.generateRandomKey();
      return `${value}_${randomKey}`;
    }
    return value;
  })
  .required()
  .label('Offering Document Type')
  .messages({
    'string.empty': '{#label} cannot be empty',
    'any.only': `{#label} must be one of the following: ${offeringDocumentTypes.join(', ')}`,
    'any.required': '{#label} is required',
  });

export const docsTypeSchema = Joi.string()
  .trim()
  .valid(...docsTypeValues)
  .required()
  .label('Document Type')
  .messages({
    'string.empty': '{#label} cannot be empty',
    'any.only': `{#label} must be one of the following: ${docsTypeValues.join(', ')}`,
    'any.required': '{#label} is required',
  });
