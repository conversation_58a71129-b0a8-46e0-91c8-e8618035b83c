import { Storage, Bucket } from '@google-cloud/storage';
import CONFIG from '../config/env';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import { DocumentTypesEnum, offeringDocumentTypesEnum, PromiseResolve } from '../utils/common.interface';
import * as path from 'path';
import axios from 'axios';
import CustomError from './customError.helper';
import logger from './logging/logger.helper';

export class CloudHelper {
  private storage: Storage;
  private bucket: Bucket;

  constructor() {
    this.initClient();
  }

  private initClient() {
    try {
      if (!CONFIG.GOOGLE.PROJECT_ID || !CONFIG.GOOGLE.BUCKET_NAME) {
        throw new Error('Google Cloud keys are not set correctly');
      }
      this.storage = new Storage({
        projectId: CONFIG.GOOGLE.PROJECT_ID,
        keyFilename: './src/helpers/valuit-tokenhub-4ec39e8b8b97.json',
      });

      this.bucket = this.storage.bucket(CONFIG.GOOGLE.BUCKET_NAME);
    } catch (error: any) {
      logger.error(error, 'File Upload Init error');
    }
  }
  public async uploadFiles(userId: string, file: Express.Multer.File, documentType: string = 'othersDocs', folderName: string, offeringId?: string): Promise<PromiseResolve | any> {
    try {
      const envFolder = CONFIG.ENVIRONMENT;
      const fileExtension = path.extname(file.originalname);
      const currentTimestamp = Date.now();
      const newFileName = `${userId}-${currentTimestamp}-${documentType}${fileExtension}`;
      let keyName = offeringId ? `${envFolder}/${folderName}/${userId}/${offeringId}/${documentType}/${newFileName}` : `${envFolder}/${folderName}/${userId}/${documentType}/${newFileName}`;

      // Construct the old folder name
      const oldFolderName = offeringId ? `${envFolder}/${folderName}/${userId}/${offeringId}/${documentType}/` : `${envFolder}/${folderName}/${userId}/${documentType}/`;

      const excludedDocumentTypes: Array<offeringDocumentTypesEnum | DocumentTypesEnum> = [
        offeringDocumentTypesEnum.url,
        DocumentTypesEnum.FRONT_ID_CARD,
        DocumentTypesEnum.BACK_ID_CARD,
        offeringDocumentTypesEnum.customImage,
      ];

      if (!excludedDocumentTypes.includes(documentType as offeringDocumentTypesEnum | DocumentTypesEnum)) {
        await this.deleteOldImages(oldFolderName);
      }

      const blob = this.bucket.file(keyName);
      const blobStream = blob.createWriteStream({
        resumable: false,
        metadata: {
          contentType: file.mimetype,
          cacheControl: 'max-age=0',
        },
      });

      return new Promise((resolve, reject) => {
        blobStream.on('error', (error: any) => {
          logger.error(error, 'Failed to upload file');
          if (error.code === 'EACCES') {
            logger.error('Permission denied while uploading to Google Cloud Storage');
          } else if (error.code === 'ENOTFOUND') {
            logger.error('Network error: Google Cloud Storage endpoint not found');
          }
          reject({
            status: error.status || RESPONSES.BAD_REQUEST,
            error: true,
            message: error.message || RES_MSG.COMMON.BAD_REQUEST,
          });
        });

        blobStream.on('finish', () => {
          const endPoint = `https://storage.googleapis.com/${CONFIG.GOOGLE.BUCKET_NAME}/${keyName}`;
          resolve({
            status: RESPONSES.SUCCESS,
            error: false,
            message: RES_MSG.SUCCESS_MSG.CREATE_SUCCESS,
            data: { url: endPoint, keyName: `${CONFIG.GOOGLE.BUCKET_NAME}/${keyName}` },
          });
        });

        blobStream.end(file.buffer);
      });
    } catch (error: any) {
      logger.error(error, 'Failed to uploadFile');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  public async deleteFile(keyName: string): Promise<boolean> {
    try {
      await this.bucket.file(keyName).delete();
      return true;
    } catch (err: any) {
      logger.error(err, 'Failed to deleteFile');
      return false;
    }
  }

  private async deleteOldImages(oldFolderName: string): Promise<void> {
    try {
      const [files] = await this.bucket.getFiles({ prefix: oldFolderName });

      if (files.length === 0) {
        logger.info(`No files found in ${oldFolderName} to delete.`);
        return;
      }

      const deletePromises = files.map((file) => file.delete());
      await Promise.all(deletePromises);
    } catch (error) {
      logger.error(error, `Failed to delete images in folder ${oldFolderName}:`);
    }
  }

  public async copyFolder(oldOfferingId: string, newOfferingId: string, userId: string, folderName: string): Promise<any> {
    try {
      // Fetch all files in the source folder
      const envFolder = CONFIG.ENVIRONMENT;
      let sourceFolderName = `${envFolder}/${folderName}/${userId}/${oldOfferingId}`;
      let destinationFolderName = `${envFolder}/${folderName}/${userId}/${newOfferingId}`;

      const [files] = await this.bucket.getFiles({ prefix: sourceFolderName });
      if (files.length === 0) {
        logger.info(`No files found in the source folder ${sourceFolderName}`);
        return {
          status: RESPONSES.NOTFOUND,
          error: true,
          message: RES_MSG.COMMON.NO_RECORD,
        };
      }

      // Prepare to copy all files
      const copyPromises = files.map(async (file) => {
        const sourceFileName = file.name;
        const destinationFileName = sourceFileName.replace(sourceFolderName, destinationFolderName);

        // Copy the file to the destination folder
        await file.copy(this.bucket.file(destinationFileName));
      });

      // Execute all copy operations
      await Promise.all(copyPromises);

      logger.info(`Successfully copied folder from ${sourceFolderName} to ${destinationFolderName}`);
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.CREATE_SUCCESS,
      };
    } catch (error: any) {
      logger.error(error, `Failed to copy folder`);
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * Fetches data from the provided URL and converts it to base64 format.
   * @param url - The URL of the file to fetch
   * @returns A Promise that resolves to the base64 string of the file
   */
  public async getFileAsBase64(url: string): Promise<PromiseResolve | any> {
    try {
      // Fetch the file data from the URL using axios
      const response = await axios.get(url, { responseType: 'arraybuffer' });

      // Convert the file data to base64 format
      const base64String = Buffer.from(response.data).toString('base64');

      return base64String;
    } catch (error: any) {
      logger.error(error, 'Failed to fetch and convert file to base64');
      throw new CustomError(error.message || 'Failed to fetch and convert file to base64', error.status);
    }
  }
}

export default new CloudHelper();
