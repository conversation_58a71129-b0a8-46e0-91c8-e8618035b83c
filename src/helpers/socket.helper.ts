import { Server, Socket } from 'socket.io';
import * as jwt from 'jsonwebtoken';
import { DecodedToken, MessagePayload, PromiseResolve, soketTypeEnum } from '../utils/common.interface';
import CONFIG from '../config/env/index';
import RedisHelper from '../helpers/redis.helper';
import OfferingService from '../component/offerings/service';
import { offeringDocs } from '../utils/constant';
import { IOffering } from '../component/offerings/models/offerings.model';
import CustomError from './customError.helper';
import { transferAgentSchema } from '../component/transferagent/transferagent.model';
// const socket = process.env.SOCKET_PORT;

class SocketHelper {
  private io: Server;

  constructor(io: Server) {
    this.io = io;
    // this.io = io;
    this.initialize();
  }

  private initialize() {
    this.io.on('connection', (socket: Socket) => {
      console.log(`⚡️[SOCKET] New connection: ${socket.id}`);

      this.authenticateUser(socket);

      socket.on('setActiveOffering', async (data: MessagePayload) => {
        try {
          const key = 'offeringId';
          await RedisHelper.setString(key, JSON.stringify(data), 3600);
          if (!data.offeringId) {
            return socket.emit('activeOfferingUpdate', { status: 'error', message: 'Missing offering ID' });
          }
          const searchQuery = { _id: data.offeringId };

          let formattedDocuments;
          const offeringDetails: PromiseResolve = await OfferingService.fetchOfferingDetails(searchQuery, [], [], true);
          if (offeringDetails.error) {
            throw new CustomError(offeringDetails.message, offeringDetails.status);
          }

          const offeringData: IOffering | any = offeringDetails.data;
          const { _id, overview, projectDetails, documents, team, currentStep, isActive, status, isTokenDeploy, tokenAddress, fundAddress, isFundDeploy } = offeringData;
          const taId = projectDetails?.taId;
          let taWalletAddress;
          if (taId) {
            const taDetails = await transferAgentSchema.findOne({ _id: taId }, 'walletAddress name email');
            taWalletAddress = taDetails || {};
          } else {
            taWalletAddress = {};
          }
          const getFileType = (url: string): string => {
            const extension = url.split('.').pop()?.toLowerCase();
            return extension;
          };

          if (documents) {
            formattedDocuments = offeringDocs.map((doc) => {
              const url = documents[doc?.name];
              if (url) {
                return {
                  title: doc.title,
                  url: url || null,
                  type: url ? getFileType(url) : 'N/A',
                };
              }
            });

            if (documents.customDocs && documents.customDocs.length > 0) {
              documents.customDocs.forEach((customDoc: { docsLabel: string; value: string }) => {
                formattedDocuments.push({
                  title: customDoc.docsLabel,
                  url: customDoc.value,
                  type: getFileType(customDoc.value),
                });
              });
            }
          }

          let cleanedDocuments = formattedDocuments?.filter(function (doc: any) {
            return doc !== null && doc !== undefined;
          });
          const profileUser = {
            _id,
            overview,
            projectDetails,
            taWalletAddress,
            documents: cleanedDocuments,
            team,
            currentStep,
            isActive,
            status,
            isTokenDeploy,
            tokenAddress,
            fundAddress,
            isFundDeploy,
            document: documents,
          };
          socket.emit('activeOfferingUpdate', { status: 'received', profileUser });
        } catch (error: any) {
          console.error('Error updating offering details:', error.message);
          socket.emit('activeOfferingUpdate', { status: 'error', message: error.message || 'Internal Server Error' });
        }
      });

      socket.on('message', (data: MessagePayload) => this.handleMessage(socket, data));
      socket.on('message', (data: any) => this.handleMessage(socket, data));
      socket.on('joinRoom', (room: string) => this.joinRoom(socket, room));
      socket.on('leaveRoom', (room: string) => this.leaveRoom(socket, room));
      socket.on('disconnect', () => this.handleDisconnect(socket));
    });
  }

  private authenticateUser(socket: Socket) {
    try {
      const token = socket.handshake.auth.token as string;
      if (!token) throw new Error('Authentication token missing');

      // this TOKEN is JWT_SECRET token to validate the authentication  of token
      const { TOKEN } = CONFIG.JWT_AUTH;
      const decoded = jwt.verify(token, TOKEN as string) as DecodedToken;
      socket.data.userId = decoded.userId || '67c035094af9851116c5b6c3';

      console.log(`✅ [SOCKET] User authenticated: ${decoded.userId}`);
    } catch (error) {
      console.error('❌ [SOCKET] Authentication failed:', error.message);
      socket.disconnect();
    }
  }

  public async emitToSocket<T>(socket: Socket, event: string, data: any) {
    try {
      let key;
      if (event == soketTypeEnum.UPDATEFORCETRANSFERRED) {
        key = `Transfer:${data._id}:status:${data.status}`;
      } else {
        key = `offering:${data.offeringId._id}:status:${data.status}`;
      }
      // Check if the key already exists in Redis
      const existingData = await RedisHelper.getString(key);
      if (existingData) {
        console.log(`⚠️ [SOCKET] Skipping duplicate event for ${key}`);
        return; // Exit function, no duplicate emissions
      }

      // Store the key in Redis with an expiration time (e.g., 1 hour)
      await RedisHelper.setString(key, JSON.stringify(data), 3600);

      this.io.emit(event, data);
    } catch (error) {
      console.error(`❌ [SOCKET] Error emitting event "${event}":`, error);
    }
  }

  public async broadcast<T>(event: string, data: any) {
    try {
      let key;
      if (event == 'offeringDetail') {
        const offeringId = data?.offeringId;
        const { latestNav } = data;
        key = `offering:${offeringId}:status:${latestNav}`;
      } else if (event == soketTypeEnum.UPDATEKYCSTATUS) {
        const userId = data?.userId;
        key = `userId:${userId}:status:${data.status}}`;
      } else {
        key = `offering:${data.offeringId._id}:status:${data.status}`;
      }
      // Check if the key already exists in Redis
      const existingData = await RedisHelper.getString(key);
      if (existingData) {
        console.log(`⚠️ [SOCKET] Skipping duplicate event for ${key}`);
        return; // Exit function, no duplicate emissions
      }
      // Store the key in Redis with an expiration time (e.g., 1 hour)
      await RedisHelper.setString(key, JSON.stringify(data), 3600);

      // Emit the event if it's a new message
      console.log('data------', data);
      this.io.emit(event, data);
      console.log(`✅ [SOCKET] Broadcasting event "${event}":`, data);
    } catch (error) {
      console.error(`❌ [SOCKET] Error broadcasting event "${event}":`, error);
    }
  }
  public async broadcastOffering<T>(event: string, data: any) {
    try {
      let key = `offeringDetail:${data._id}`;
      if (!data._id) {
        return this.io.emit('activeOfferingUpdate', { status: 'error', message: 'Missing offering ID' });
      }
      const searchQuery = { _id: data._id };

      let formattedDocuments;
      const offeringDetails: PromiseResolve = await OfferingService.fetchOfferingDetails(searchQuery, [], [], true);
      if (offeringDetails.error) {
        throw new CustomError(offeringDetails.message, offeringDetails.status);
      }
      const offeringData: IOffering | any = offeringDetails.data;
      const { _id, overview, projectDetails, documents, team, currentStep, isActive, status, isTokenDeploy, tokenAddress, fundAddress, isFundDeploy } = offeringData;
      const taId = projectDetails?.taId;
      let taWalletAddress;
      if (taId) {
        const taDetails = await transferAgentSchema.findOne({ _id: taId }, 'walletAddress name email');
        taWalletAddress = taDetails || {};
      } else {
        taWalletAddress = {};
      }
      const getFileType = (url: string): string => {
        const extension = url.split('.').pop()?.toLowerCase();
        return extension;
      };

      if (documents) {
        formattedDocuments = offeringDocs.map((doc) => {
          const url = documents[doc?.name];
          if (url) {
            return {
              title: doc.title,
              url: url || null,
              type: url ? getFileType(url) : 'N/A',
            };
          }
        });

        if (documents.customDocs && documents.customDocs.length > 0) {
          documents.customDocs.forEach((customDoc: { docsLabel: string; value: string }) => {
            formattedDocuments.push({
              title: customDoc.docsLabel,
              url: customDoc.value,
              type: getFileType(customDoc.value),
            });
          });
        }
      }

      let cleanedDocuments = formattedDocuments?.filter(function (doc: any) {
        return doc !== null && doc !== undefined;
      });
      const profileUser = {
        _id,
        overview,
        projectDetails,
        taWalletAddress,
        documents: cleanedDocuments,
        team,
        currentStep,
        isActive,
        status,
        isTokenDeploy,
        tokenAddress,
        fundAddress,
        isFundDeploy,
        document: documents,
      };
      const existingData = await RedisHelper.getString(key);
      if (existingData) {
        console.log(`⚠️ [SOCKET] Skipping duplicate event for ${key}`);
        return; // Exit function, no duplicate emissions
      }
      // Store the key in Redis with an expiration time (e.g., 1 hour)
      await RedisHelper.setString(key, JSON.stringify(data), 300);

      // Emit the event if it's a new message
      this.io.emit('activeOfferingUpdate', { status: 'received', profileUser });
    } catch (error) {
      console.error(`❌ [SOCKET] Error broadcasting event "${event}":`, error);
    }
  }

  public sendMessageToUserWithoutAuth(userId: string, event: string, data: any) {
    const socket = this.findSocketByUserId(userId);
    if (socket) {
      this.emitToSocket(socket, event, data);
    } else if (event == 'activeOfferingUpdate') {
      this.broadcastOffering(event, data);
    } else {
      console.warn(`⚠️ [SOCKET] User ${userId} not connected, broadcasting message`);
      this.broadcast(event, data); // Send message to all connected users if a specific user isn't found
    }
  }

  public sendMessageToUser(userId: string, event: string, data: any) {
    const socket = this.findSocketByUserId(userId);
    if (socket) {
      this.emitToSocket(socket, event, data);
    } else {
      console.warn(`⚠️ [SOCKET] User ${userId} not connected`);
    }
  }

  private handleMessage(socket: Socket, data: any) {
    console.log(`📩 [SOCKET] Message received from ${socket.id}:`, data);
    this.broadcast('message', data);
  }

  private findSocketByUserId(userId: string): Socket | undefined {
    return Array.from(this.io.sockets.sockets.values()).find((socket) => socket.data.userId === userId);
  }

  private joinRoom(socket: Socket, room: string) {
    socket.join(room);
    console.log(`👥 [SOCKET] User ${socket.id} joined room: ${room}`);
    this.io.to(room).emit('userJoined', { userId: socket.data.userId, room });
  }

  private leaveRoom(socket: Socket, room: string) {
    socket.leave(room);
    console.log(`🚪 [SOCKET] User ${socket.id} left room: ${room}`);
    this.io.to(room).emit('userLeft', { userId: socket.data.userId, room });
  }

  private handleDisconnect(socket: Socket) {
    console.log(`🔌 [SOCKET] User disconnected: ${socket.id}`);
  }
}

// Create a global instance
export let socketHelper: SocketHelper;

export const initializeSocket = (io: Server) => {
  if (!socketHelper) {
    socketHelper = new SocketHelper(io);
  }
};
