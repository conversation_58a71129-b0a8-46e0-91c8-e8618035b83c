import mongoose, { Connection, Mongoose } from 'mongoose';
import CONFIG from '../env/index';
import logger from '../../helpers/logging/logger.helper';

class Db {
  public mongooseInstance: Mongoose = mongoose;
  public dbUri: string;
  // = CONFIG.ENVIRONMENT === 'development' ? `mongodb://${CONFIG.DATABASE.MONGODB_HOST}:${CONFIG.DATABASE.MONGODB_PORT}/${CONFIG.DATABASE.MONGODB_DATABASE}?authSource=admin` : `mongodb://${CONFIG.DATABASE.MONGODB_USER}:${CONFIG.DATABASE.MONGODB_PASSWORD}@${CONFIG.DATABASE.MONGODB_HOST}:${CONFIG.DATABASE.MONGODB_PORT}/${CONFIG.DATABASE.MONGODB_DATABASE}?authSource=admin`;
  public dbConnection = async (): Promise<void> => {
    try {
      if (CONFIG.DATABASE.MONGODB_USER && CONFIG.DATABASE.MONGODB_PASSWORD) {
        this.dbUri = `mongodb://${CONFIG.DATABASE.MONGODB_USER}:${CONFIG.DATABASE.MONGODB_PASSWORD}@${CONFIG.DATABASE.MONGODB_HOST}:${CONFIG.DATABASE.MONGODB_PORT}/${CONFIG.DATABASE.MONGODB_DATABASE}?authSource=admin`;
      } else {
        this.dbUri = `mongodb://${CONFIG.DATABASE.MONGODB_HOST}:${CONFIG.DATABASE.MONGODB_PORT}/${CONFIG.DATABASE.MONGODB_DATABASE}?authSource=admin`;
      }
      await this.mongooseInstance.connect(this.dbUri, {
        socketTimeoutMS: 0,
        connectTimeoutMS: 30000,
      });

      const dbConnection: Connection = this.mongooseInstance.connection;
      dbConnection.on('error', console.error.bind(console, 'Connection error:'));
      dbConnection.once('open', () => {
        logger.info('Database connected successfully');
      });

      this.mongooseInstance.set('strictQuery', false);
    } catch (error) {
      logger.error(error, 'Error connecting to the database:');
    }
  };

  public dbDisconnect = async (): Promise<void> => {
    try {
      await this.mongooseInstance.disconnect();
      logger.info('Database disconnected successfully');
    } catch (error) {
      logger.error(error, 'Error disconnecting from the database:');
    }
  };
}

export default new Db();
