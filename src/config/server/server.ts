import * as express from 'express';
import * as Middleware from '../middleware/middleware';
import * as Routes from '../../routes';
import RedisHelper from '../../helpers/redis.helper';
import Db from '../connection/connection'; // Adjust the import path as necessary
import logger from '../../helpers/logging/logger.helper';
import kafkaService from '../../services/kafkaService';
const seedUsers = require('../../seed/userSeeder');
const migrateModules = require('../../seed/modulesSeeder');
const migrateRoutes = require('../../seed/frontRoute');
import { Server } from 'socket.io';
import { initializeSocket } from '../../helpers/socket.helper'; // Import the singleton getter

import GrpcServer from '../../_grpc';
import CONFIG from '../env/';

// import Grpc from '../../_grpc/server';

/**
 * @constant {express.Application}
 */
const app: express.Application = express();

/**
 * @constructs express.Application Middleware
 */
Middleware.configure(app);

/**
 * @constructs express.Application Routes
 */
Routes.init(app);

/**
 * @constructs express.Application Error Handler
 */
Middleware.initErrorHandler(app);

/**
 * Function to start the server
 */
const startServer = async () => {
  try {
    // Establish database connection
    await Db.dbConnection();
    await seedUsers();
    await migrateModules();
    await migrateRoutes();

    // Sets port 3000 to default or unless otherwise specified in the environment
    app.set('port', process.env.PORT || 7002);
    const port = app.get('port');

    app.listen(port, () => {
      logger.info(`Admin Service Server is running on http://localhost:${port}`);
    });

    const grpcServer = new GrpcServer(CONFIG.GRPC.ADMIN_SERVICE_GRPC_CONTAINER_NAME.toString(), CONFIG.GRPC.ADMIN_SERVICE_GRPC_PORT.toString());
    grpcServer.start();
    // Connect to Redis with a slight delay
    setTimeout(() => {
      RedisHelper.connectRedis();
      kafkaService;
    }, 200); // 200 milliseconds
    const io = new Server(Number(CONFIG.SOCKET_PORT), { cors: { origin: '*' } });
    initializeSocket(io); // ✅ Initialize once before other files use it
    logger.info(`Socket Helper is started on port ${CONFIG.SOCKET_PORT}`);
  } catch (error) {
    logger.error('Failed to start the server', error);
  }
};

/**
 * Start the server
 */
startServer();

/**
 * Handle graceful shutdown
 */
process.on('SIGINT', async () => {
  await Db.dbDisconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await Db.dbDisconnect();
  process.exit(0);
});

/**
 * @exports {express.Application}
 */
export default app;
