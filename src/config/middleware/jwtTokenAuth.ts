import { Request, Response, NextFunction } from 'express';
import jwt, { JwtPayload } from 'jsonwebtoken';
import CommonHelper from '../../helpers/common.helper';
import { PromiseResolve } from '../../utils/common.interface';
import CustomError from '../../helpers/customError.helper';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
interface AuthRequest extends Request {
  user?: string | JwtPayload; // Extend the Request interface to include the user property
}
const authMiddleware = async (req: AuthRequest, res: Response, next: NextFunction): Promise<any> => {
  const token = req.body.token;

  try {
    // Verify the token
    const isTokenValid: PromiseResolve = await CommonHelper.isValidToken(token);
    if (isTokenValid.error) throw new CustomError(RES_MSG.TWO_FA.TOKEN_EXPIRE, RESPONSES.UN_AUTHORIZED);
    req.body.user = isTokenValid?.data; // Attach decoded token to the request object
    req.body.token = token;
    next(); // Proceed to the next middleware or route handler
  } catch (error) {
    return res.status(403).json({ message: 'Invalid or expired token' });
  }
};
export default authMiddleware;
