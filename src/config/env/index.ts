import * as dotenv from 'dotenv';
dotenv.config();

interface IConfig {
  ENVIRONMENT: string;
  PROJECT: {
    NAME: string;
    LOG_LEVEL: string;
  };
  DATABASE: {
    MONGODB_HOST: string;
    MONGODB_USER: string;
    MONGODB_PASSWORD: string;
    MONGODB_PORT: number;
    MONGODB_DATABASE: string;
  };
  REDIS: {
    HOST: string;
    LOGIN_MAX_ATTEMPT: number;
    LOGIN_BLOCK_TIME: number;
    REDIS_AUTH_EXPIRE: number;
    OTP_EXPIRY: number;
  };
  JWT_AUTH: {
    TOKEN: string;
    AUTH_EXPIRE_TIME: string;
    REFRESH_TOKEN: string;
    REFRESH_EXPIRE_TIME: string;
    FORGOT_EXPIRE_TIME: string;
    JWT_2FA_EXPIRE: string;
  };
  SENDGRID: {
    API_KEY: string;
    SENDER: string;
  };
  GOOGLE: {
    CLIENT_ID: string;
    PROJECT_ID: string;
    BUCKET_NAME: string;
    UPLOAD_URL: String;
    MAX_SIZE: Number;
  };
  KAFKA: {
    BROKERS: string;
  };
  GRPC: {
    USER_SERVICE_GRPC_CONTAINER_NAME: String;
    USER_SERVICE_GRPC_PORT: String;

    ADMIN_SERVICE_GRPC_CONTAINER_NAME: String;
    ADMIN_SERVICE_GRPC_PORT: String;

    NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME: string;
    NOTIFICATION_SERVICE_GRPC_PORT: string;

    GRPC_SSL: String;
  };
  SOCKET_PORT: string;
}

const NODE_ENV: string = process.env.NODE_ENV || 'development';

const development: IConfig = {
  SOCKET_PORT: process.env.SOCKET_PORT,
  ENVIRONMENT: NODE_ENV,
  PROJECT: {
    NAME: process.env.PROJECT_NAME,
    LOG_LEVEL: process.env.LOG_LEVEL,
  },
  DATABASE: {
    MONGODB_HOST: process.env.MONGODB_HOST || 'localhost',
    MONGODB_USER: process.env.MONGODB_USER,
    MONGODB_PASSWORD: process.env.MONGODB_PASSWORD,
    MONGODB_PORT: Number(process.env.MONGODB_PORT) || 27017,
    MONGODB_DATABASE: process.env.MONGODB_DATABASE || 'mvp_db',
  },

  REDIS: {
    HOST: process.env.REDIS_HOST,
    LOGIN_MAX_ATTEMPT: Number(process.env.LOGIN_MAX_ATTEMPT),
    LOGIN_BLOCK_TIME: Number(process.env.LOGIN_BLOCK_TIME),
    REDIS_AUTH_EXPIRE: Number(process.env.REDIS_AUTH_EXPIRE),
    OTP_EXPIRY: Number(process.env.OTP_EXPIRY),
  },
  JWT_AUTH: {
    TOKEN: process.env.JWT_AUTH_SECRET,
    AUTH_EXPIRE_TIME: process.env.JWT_AUTH_EXPIRE,
    REFRESH_TOKEN: process.env.JWT_REFRESH_SECRET,
    REFRESH_EXPIRE_TIME: process.env.JWT_REFRESH_EXPIRE,
    FORGOT_EXPIRE_TIME: process.env.JWT_FORGOT_EXPIRE,
    JWT_2FA_EXPIRE: process.env.JWT_2FA_EXPIRE,
  },
  SENDGRID: {
    API_KEY: process.env.SENDGRID_API_KEY,
    SENDER: process.env.SENDER,
  },
  GOOGLE: {
    CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
    PROJECT_ID: process.env.GOOGLE_PROJECT_ID,
    BUCKET_NAME: process.env.BUCKET_NAME,
    UPLOAD_URL: `https://storage.googleapis.com/${process.env.GOOGLE_KEY_FILE_NAME}/`,
    MAX_SIZE: Number(process.env.MAX_SIZE || 5),
  },
  KAFKA: {
    BROKERS: `${process.env.KAFKA_BROKER}:${process.env.KAFKA_BROKER_PORT}`,
  },
  GRPC: {
    USER_SERVICE_GRPC_CONTAINER_NAME: process.env.USER_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    USER_SERVICE_GRPC_PORT: process.env.USER_SERVICE_GRPC_PORT || '4001',

    ADMIN_SERVICE_GRPC_CONTAINER_NAME: process.env.ADMIN_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    ADMIN_SERVICE_GRPC_PORT: process.env.ADMIN_SERVICE_GRPC_PORT || '7001',

    NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME: process.env.NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    NOTIFICATION_SERVICE_GRPC_PORT: process.env.NOTIFICATION_SERVICE_GRPC_PORT || '8001',
    GRPC_SSL: process.env.GRPC_SSL,
  },
};
const stage: IConfig = {
  SOCKET_PORT: process.env.SOCKET_PORT,
  ENVIRONMENT: NODE_ENV,
  PROJECT: {
    NAME: process.env.PROJECT_NAME,
    LOG_LEVEL: process.env.LOG_LEVEL,
  },
  DATABASE: {
    MONGODB_HOST: process.env.MONGODB_HOST || 'localhost',
    MONGODB_USER: process.env.MONGODB_USER,
    MONGODB_PASSWORD: process.env.MONGODB_PASSWORD,
    MONGODB_PORT: Number(process.env.MONGODB_PORT) || 27017,
    MONGODB_DATABASE: process.env.MONGODB_DATABASE || 'mvp_db',
  },

  REDIS: {
    HOST: process.env.REDIS_HOST,
    LOGIN_MAX_ATTEMPT: Number(process.env.LOGIN_MAX_ATTEMPT),
    LOGIN_BLOCK_TIME: Number(process.env.LOGIN_BLOCK_TIME),
    REDIS_AUTH_EXPIRE: Number(process.env.REDIS_AUTH_EXPIRE),
    OTP_EXPIRY: Number(process.env.OTP_EXPIRY),
  },
  JWT_AUTH: {
    TOKEN: process.env.JWT_AUTH_SECRET,
    AUTH_EXPIRE_TIME: process.env.JWT_AUTH_EXPIRE,
    REFRESH_TOKEN: process.env.JWT_REFRESH_SECRET,
    REFRESH_EXPIRE_TIME: process.env.JWT_REFRESH_EXPIRE,
    FORGOT_EXPIRE_TIME: process.env.JWT_FORGOT_EXPIRE,
    JWT_2FA_EXPIRE: process.env.JWT_2FA_EXPIRE,
  },
  SENDGRID: {
    API_KEY: process.env.SENDGRID_API_KEY,
    SENDER: process.env.SENDER,
  },
  GOOGLE: {
    CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
    PROJECT_ID: process.env.GOOGLE_PROJECT_ID,
    BUCKET_NAME: process.env.BUCKET_NAME,
    UPLOAD_URL: `https://storage.googleapis.com/${process.env.GOOGLE_KEY_FILE_NAME}/`,
    MAX_SIZE: Number(process.env.MAX_SIZE || 5),
  },
  KAFKA: {
    BROKERS: `${process.env.KAFKA_BROKER}:${process.env.KAFKA_BROKER_PORT}`,
  },
  GRPC: {
    USER_SERVICE_GRPC_CONTAINER_NAME: process.env.USER_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    USER_SERVICE_GRPC_PORT: process.env.USER_SERVICE_GRPC_PORT || '4001',

    ADMIN_SERVICE_GRPC_CONTAINER_NAME: process.env.ADMIN_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    ADMIN_SERVICE_GRPC_PORT: process.env.ADMIN_SERVICE_GRPC_PORT || '7001',
    NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME: process.env.NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    NOTIFICATION_SERVICE_GRPC_PORT: process.env.NOTIFICATION_SERVICE_GRPC_PORT || '8001',
    GRPC_SSL: process.env.GRPC_SSL,
  },
};
const qa: IConfig = {
  SOCKET_PORT: process.env.SOCKET_PORT,
  ENVIRONMENT: NODE_ENV,
  PROJECT: {
    NAME: process.env.PROJECT_NAME,
    LOG_LEVEL: process.env.LOG_LEVEL,
  },
  DATABASE: {
    MONGODB_HOST: process.env.MONGODB_HOST || 'localhost',
    MONGODB_USER: process.env.MONGODB_USER,
    MONGODB_PASSWORD: process.env.MONGODB_PASSWORD,
    MONGODB_PORT: Number(process.env.MONGODB_PORT) || 27017,
    MONGODB_DATABASE: process.env.MONGODB_DATABASE || 'mvp_db',
  },

  REDIS: {
    HOST: process.env.REDIS_HOST,
    LOGIN_MAX_ATTEMPT: Number(process.env.LOGIN_MAX_ATTEMPT),
    LOGIN_BLOCK_TIME: Number(process.env.LOGIN_BLOCK_TIME),
    REDIS_AUTH_EXPIRE: Number(process.env.REDIS_AUTH_EXPIRE),
    OTP_EXPIRY: Number(process.env.OTP_EXPIRY),
  },
  JWT_AUTH: {
    TOKEN: process.env.JWT_AUTH_SECRET,
    AUTH_EXPIRE_TIME: process.env.JWT_AUTH_EXPIRE,
    REFRESH_TOKEN: process.env.JWT_REFRESH_SECRET,
    REFRESH_EXPIRE_TIME: process.env.JWT_REFRESH_EXPIRE,
    FORGOT_EXPIRE_TIME: process.env.JWT_FORGOT_EXPIRE,
    JWT_2FA_EXPIRE: process.env.JWT_2FA_EXPIRE,
  },
  SENDGRID: {
    API_KEY: process.env.SENDGRID_API_KEY,
    SENDER: process.env.SENDER,
  },
  GOOGLE: {
    CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
    PROJECT_ID: process.env.GOOGLE_PROJECT_ID,
    BUCKET_NAME: process.env.BUCKET_NAME,
    UPLOAD_URL: `https://storage.googleapis.com/${process.env.GOOGLE_KEY_FILE_NAME}/`,
    MAX_SIZE: Number(process.env.MAX_SIZE || 5),
  },
  KAFKA: {
    BROKERS: `${process.env.KAFKA_BROKER}:${process.env.KAFKA_BROKER_PORT}`,
  },
  GRPC: {
    USER_SERVICE_GRPC_CONTAINER_NAME: process.env.USER_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    USER_SERVICE_GRPC_PORT: process.env.USER_SERVICE_GRPC_PORT || '4001',

    ADMIN_SERVICE_GRPC_CONTAINER_NAME: process.env.ADMIN_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    ADMIN_SERVICE_GRPC_PORT: process.env.ADMIN_SERVICE_GRPC_PORT || '7001',
    NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME: process.env.NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    NOTIFICATION_SERVICE_GRPC_PORT: process.env.NOTIFICATION_SERVICE_GRPC_PORT || '8001',
    GRPC_SSL: process.env.GRPC_SSL,
  },
};
const uat: IConfig = {
  SOCKET_PORT: process.env.SOCKET_PORT,
  ENVIRONMENT: NODE_ENV,
  PROJECT: {
    NAME: process.env.PROJECT_NAME,
    LOG_LEVEL: process.env.LOG_LEVEL,
  },
  DATABASE: {
    MONGODB_HOST: process.env.MONGODB_HOST || 'localhost',
    MONGODB_USER: process.env.MONGODB_USER,
    MONGODB_PASSWORD: process.env.MONGODB_PASSWORD,
    MONGODB_PORT: Number(process.env.MONGODB_PORT) || 27017,
    MONGODB_DATABASE: process.env.MONGODB_DATABASE || 'mvp_db',
  },

  REDIS: {
    HOST: process.env.REDIS_HOST,
    LOGIN_MAX_ATTEMPT: Number(process.env.LOGIN_MAX_ATTEMPT),
    LOGIN_BLOCK_TIME: Number(process.env.LOGIN_BLOCK_TIME),
    REDIS_AUTH_EXPIRE: Number(process.env.REDIS_AUTH_EXPIRE),
    OTP_EXPIRY: Number(process.env.OTP_EXPIRY),
  },
  JWT_AUTH: {
    TOKEN: process.env.JWT_AUTH_SECRET,
    AUTH_EXPIRE_TIME: process.env.JWT_AUTH_EXPIRE,
    REFRESH_TOKEN: process.env.JWT_REFRESH_SECRET,
    REFRESH_EXPIRE_TIME: process.env.JWT_REFRESH_EXPIRE,
    FORGOT_EXPIRE_TIME: process.env.JWT_FORGOT_EXPIRE,
    JWT_2FA_EXPIRE: process.env.JWT_2FA_EXPIRE,
  },
  SENDGRID: {
    API_KEY: process.env.SENDGRID_API_KEY,
    SENDER: process.env.SENDER,
  },
  GOOGLE: {
    CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
    PROJECT_ID: process.env.GOOGLE_PROJECT_ID,
    BUCKET_NAME: process.env.BUCKET_NAME,
    UPLOAD_URL: `https://storage.googleapis.com/${process.env.GOOGLE_KEY_FILE_NAME}/`,
    MAX_SIZE: Number(process.env.MAX_SIZE || 5),
  },
  KAFKA: {
    BROKERS: `${process.env.KAFKA_BROKER}:${process.env.KAFKA_BROKER_PORT}`,
  },
  GRPC: {
    USER_SERVICE_GRPC_CONTAINER_NAME: process.env.USER_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    USER_SERVICE_GRPC_PORT: process.env.USER_SERVICE_GRPC_PORT || '4001',

    ADMIN_SERVICE_GRPC_CONTAINER_NAME: process.env.ADMIN_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    ADMIN_SERVICE_GRPC_PORT: process.env.ADMIN_SERVICE_GRPC_PORT || '7001',
    NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME: process.env.NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    NOTIFICATION_SERVICE_GRPC_PORT: process.env.NOTIFICATION_SERVICE_GRPC_PORT || '8001',
    GRPC_SSL: process.env.GRPC_SSL,
  },
};

const production: IConfig = {
  ENVIRONMENT: NODE_ENV,
  PROJECT: {
    NAME: process.env.PROJECT_NAME,
    LOG_LEVEL: process.env.LOG_LEVEL,
  },
  DATABASE: {
    MONGODB_HOST: process.env.MONGODB_HOST || 'localhost',
    MONGODB_USER: process.env.MONGODB_USER,
    MONGODB_PASSWORD: process.env.MONGODB_PASSWORD,
    MONGODB_PORT: Number(process.env.MONGODB_PORT) || 27017,
    MONGODB_DATABASE: process.env.MONGODB_DATABASE || 'mvp_db',
  },

  REDIS: {
    HOST: process.env.REDIS_HOST,
    LOGIN_MAX_ATTEMPT: Number(process.env.LOGIN_MAX_ATTEMPT),
    LOGIN_BLOCK_TIME: Number(process.env.LOGIN_BLOCK_TIME),
    REDIS_AUTH_EXPIRE: Number(process.env.REDIS_AUTH_EXPIRE),
    OTP_EXPIRY: Number(process.env.OTP_EXPIRY),
  },
  JWT_AUTH: {
    TOKEN: process.env.JWT_AUTH_SECRET,
    AUTH_EXPIRE_TIME: process.env.JWT_AUTH_EXPIRE,
    REFRESH_TOKEN: process.env.JWT_REFRESH_SECRET,
    REFRESH_EXPIRE_TIME: process.env.JWT_REFRESH_EXPIRE,
    FORGOT_EXPIRE_TIME: process.env.JWT_FORGOT_EXPIRE,
    JWT_2FA_EXPIRE: process.env.JWT_2FA_EXPIRE,
  },
  SENDGRID: {
    API_KEY: process.env.SENDGRID_API_KEY,
    SENDER: process.env.SENDER,
  },
  GOOGLE: {
    CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
    PROJECT_ID: process.env.GOOGLE_PROJECT_ID,
    BUCKET_NAME: process.env.BUCKET_NAME,
    UPLOAD_URL: `https://storage.googleapis.com/${process.env.GOOGLE_KEY_FILE_NAME}/`,
    MAX_SIZE: Number(process.env.MAX_SIZE || 5),
  },
  KAFKA: {
    BROKERS: `${process.env.KAFKA_BROKER}:${process.env.KAFKA_BROKER_PORT}`,
  },
  GRPC: {
    USER_SERVICE_GRPC_CONTAINER_NAME: process.env.USER_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    USER_SERVICE_GRPC_PORT: process.env.USER_SERVICE_GRPC_PORT || '4001',

    ADMIN_SERVICE_GRPC_CONTAINER_NAME: process.env.ADMIN_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    ADMIN_SERVICE_GRPC_PORT: process.env.ADMIN_SERVICE_GRPC_PORT || '7001',
    NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME: process.env.NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    NOTIFICATION_SERVICE_GRPC_PORT: process.env.NOTIFICATION_SERVICE_GRPC_PORT || '8001',
    GRPC_SSL: process.env.GRPC_SSL,
  },
  SOCKET_PORT: process.env.SOCKET_PORT,
};

const config: { [name: string]: IConfig } = {
  development,
  production,
  stage,
  qa,
  uat,
};

export default config[NODE_ENV];
