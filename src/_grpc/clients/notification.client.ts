import CONFIG from '../../config/env';
const grpc = require('@grpc/grpc-js');
const protoLoader = require('@grpc/proto-loader');
// import path from 'path';
const path = require('path');

const PROTO_PATH = path.join(__dirname, '../proto/user.proto');
const PROTO_OPTIONS = {
  keepCase: true,
  longs: String,
  enums: String,
  arrays: true,
};

class NotificationClient {
  public client: any;

  constructor() {
    this.connectNotificationClient();
  }

  public async connectNotificationClient() {
    const host = CONFIG.GRPC.NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME;
    const port = CONFIG.GRPC.NOTIFICATION_SERVICE_GRPC_PORT;
    const isSsl = process.env.GRPC_SSL;

    const packageDefinition = protoLoader.loadSync(PROTO_PATH, PROTO_OPTIONS);
    const grpcObject: any = grpc.loadPackageDefinition(packageDefinition);

    // Access the UserService from the 'user' package
    const AdminGrpcService = grpcObject.user.AdminGrpcService;

    this.client = new AdminGrpcService(`${host}:${port}`, isSsl === 'True' ? grpc.credentials.createSsl() : grpc.credentials.createInsecure());

    console.log(`AdminGrpc Service Client running at ${host}:${port}`);
  }
}

export default new NotificationClient();
