import { loadSync } from '@grpc/proto-loader';
import { loadPackageDefinition, Server, ServerCredentials, ServerUnaryCall, sendUnaryData, status as grpcStatus } from '@grpc/grpc-js';
import * as path from 'path';
import CONFIG from '../config/env';
import { PromiseResolve } from '../utils/common.interface';
import { RES_MSG } from '../utils/responseUtils';
import logger from '../helpers/logging/logger.helper';
import transferService from '../component/transferagent/service';
import transactionsService from '../component/transactions/service';
import UserService from '../component/userAuthentications/service';
// Constants for proto loading
const PROTO_PATH = path.join(__dirname, '/proto/user.proto');
const PROTO_OPTIONS = {
  keepCase: true,
  longs: String,
  enums: String,
  arrays: true,
};

// Helper to load proto definition
function loadProto() {
  const packageDefinition = loadSync(PROTO_PATH, PROTO_OPTIONS);
  return loadPackageDefinition(packageDefinition);
}

class GrpcServer {
  private server: Server;
  private host: string;
  private port: string;

  constructor(host: string = CONFIG.GRPC.ADMIN_SERVICE_GRPC_CONTAINER_NAME.toString(), port: string = CONFIG.GRPC.ADMIN_SERVICE_GRPC_PORT.toString()) {
    this.host = host;
    this.port = port;
    this.server = new Server();
  }

  /**
   * Initialize and start the gRPC server
   */

  public async start() {
    const proto: any = loadProto();
    const adminGrpcService = proto.user.AdminGrpcService.service;

    if (!adminGrpcService) {
      logger.error('AdminGrpcService is not defined in the proto file');
      return;
    }

    this.addServices(adminGrpcService);
    this.bindServer();
  }

  /**
   * Bind gRPC server to host and port
   */
  private bindServer() {
    this.server.bindAsync(`${this.host}:${this.port}`, ServerCredentials.createInsecure(), (error: Error | null) => {
      if (error) {
        logger.error('Failed to bind gRPC server:', error.message);
        return;
      }
      logger.info(`gRPC server started on ${this.host}:${this.port}`);
      this.server.start();
    });
  }

  /**
   * Add services to the gRPC server
   * @param userService - gRPC service definition for user operations
   */
  private addServices(userService: any) {
    this.server.addService(userService, {
      getTransferAgent: this.getTransferAgentHandler.bind(this),
      getTransferAgentDetails: this.getTransferDetailsAgentHandler.bind(this),
      getExistingUser: this.getExistingUserHandler.bind(this),
    });
  }

  /**
   * get ta list using gRPC call
   * @param call - Incoming gRPC call
   * @param callback - gRPC callback function to send the response
   */
  private async getTransferAgentHandler(call: ServerUnaryCall<any, any>, callback: sendUnaryData<any>) {
    try {
      // const req = call.request;
      const { page = 1, limit = 10, sort = JSON.stringify({ createdAt: -1 }), search = '', isActive = true } = call.request;
      const filters = {
        ...(isActive !== undefined && { isActive }),
      };
      const projection = ['name', 'email', 'isActive', 'status', 'walletAddress', 'createdAt'];
      const result: PromiseResolve = await transferService.fetchTransferAgentList(filters, projection, {
        page: Number(page),
        limit: Number(limit),
        // sort: sort,
        search,
      });
      const data = result.data ? JSON.stringify(result.data) : null;
      callback(null, {
        status: result.status,
        error: result.error,
        message: result.message,
        data: data,
      });
    } catch (error: any) {
      logger.error('getTransferAgentHandler Error:', error);
      callback(
        {
          code: grpcStatus.INTERNAL,
          message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        },
        null,
      );
    }
  }

  /**
   * get ta list using gRPC call
   * @param call - Incoming gRPC call
   * @param callback - gRPC callback function to send the response
   */
  private async getTransferDetailsAgentHandler(call: ServerUnaryCall<any, any>, callback: sendUnaryData<any>) {
    try {
      // const req = call.request;
      const { taId } = call.request;

      const result: PromiseResolve = await transferService.fetchTaDetails(taId);
      const data = result.data ? JSON.stringify(result.data) : null;
      callback(null, {
        status: result.status,
        error: result.error,
        message: result.message,
        data: data,
      });
    } catch (error: any) {
      logger.error('getTransferDetailsAgentHandler Error:', error);
      callback(
        {
          code: grpcStatus.INTERNAL,
          message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        },
        null,
      );
    }
  }

  /**
   * Get Existing User using gRPC call
   * @param call - Incoming gRPC call
   * @param callback - gRPC callback function to send the response
   */
  private async getExistingUserHandler(call: ServerUnaryCall<any, any>, callback: sendUnaryData<any>) {
    try {
      // const req = call.request;
      const { search = '' } = call.request;
      const result: PromiseResolve = await UserService.searchUsersAndAgents(search?.toLowerCase());
      const data = result.data ? JSON.stringify(result.data) : null;
      callback(null, {
        status: result.status,
        error: result.error,
        message: result.message,
        data: data,
      });
    } catch (error: any) {
      logger.error('getExistingUserHandler Error:', error);
      callback(
        {
          code: grpcStatus.INTERNAL,
          message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        },
        null,
      );
    }
  }
}

export default GrpcServer;
