NODE_ENV=development
PROJECT_NAME=
LOG_LEVEL="debug"

PORT=

MONGODB_HOST=
MONGODB_PORT=
MONGODB_DATABASE=

REDIS_HOST=
LOGIN_MAX_ATTEMPT=
LOGIN_BLOCK_TIME=           #IN SECONDS
REDIS_AUTH_EXPIRE=            # "Set REDIS_AUTH_EXPIRE time in seconds equal to JWT_AUTH_EXPIRE."
OTP_EXPIRY=

JWT_AUTH_SECRET=
JWT_AUTH_EXPIRE=                        # 1 hour
JWT_REFRESH_SECRET=
JWT_REFRESH_EXPIRE=                   # 1 day
JWT_FORGOT_EXPIRE=                      # 10 minutes
JWT_2FA_EXPIRE=                         # 10 minutes
JWT_EMAIL_FORGOT_EXPIRE=                # 10 minutes
JWT_EMAIL_LOGIN_EXPIRE=                 # 10 minutes

SENDGRID_API_KEY=
SENDER=

GOOGLE_CLIENT_ID=
MAX_SIZE=5
GOOGLE_PROJECT_ID=
BUCKET_NAME=
